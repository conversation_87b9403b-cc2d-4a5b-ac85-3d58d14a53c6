/**
 * 🎰 NATURAL REEL ANIMATOR - Realistic Slot Machine Physics
 * Creates varied, unpredictable reel patterns that feel natural
 */

export interface ReelAnimationConfig {
  reelIndex: number;
  baseSpinTime: number;
  variationRange: number;
  anticipationMode: boolean;
  scatterCount: number;
  luckScore: number;
}

export interface AnimationPhase {
  phase: 'startup' | 'spinning' | 'slowdown' | 'stopping' | 'stopped';
  duration: number;
  speed: number;
  symbolChangeInterval: number;
}

export class NaturalReelAnimator {
  private reelElements: HTMLElement[] = [];
  private animationStates = new Map<number, AnimationPhase>();
  private symbolPools: string[][] = [];
  private currentSymbols: string[][] = [];
  
  // 🎯 Natural timing variations
  private readonly BASE_SPIN_TIMES = [1200, 1400, 1600, 1800, 2000]; // Different base times per reel
  private readonly VARIATION_RANGES = [200, 250, 300, 350, 400]; // Random variation per reel
  private readonly ANTICIPATION_DELAYS = [500, 800, 1200, 1500, 2000]; // Extra delays for anticipation
  
  constructor() {
    this.initializeSymbolPools();
  }

  /**
   * 🎯 Initialize varied symbol pools for each reel
   */
  private initializeSymbolPools(): void {
    // Each reel has slightly different symbol distribution for variety
    this.symbolPools = [
      // Reel 1: Balanced
      ['9', '10', 'J', 'Q', 'K', 'A', 'WILD', 'SCATTER'],
      // Reel 2: More low symbols
      ['9', '9', '10', '10', 'J', 'Q', 'K', 'A', 'WILD', 'SCATTER'],
      // Reel 3: Balanced with slight high bias
      ['9', '10', 'J', 'J', 'Q', 'Q', 'K', 'A', 'WILD', 'SCATTER'],
      // Reel 4: More medium symbols
      ['9', '10', 'J', 'Q', 'Q', 'K', 'K', 'A', 'WILD', 'SCATTER'],
      // Reel 5: Slightly more high symbols
      ['9', '10', 'J', 'Q', 'K', 'K', 'A', 'A', 'WILD', 'SCATTER']
    ];
  }

  /**
   * 🎰 Start natural reel animation with varied patterns
   */
  public async animateReels(
    finalGrid: string[][],
    config: {
      anticipationMode?: boolean;
      scatterCount?: number;
      luckScore?: number;
      megaJackpot?: boolean;
    } = {}
  ): Promise<void> {
    this.reelElements = Array.from(document.querySelectorAll('.reel-column'));
    
    if (this.reelElements.length === 0) {
      console.warn('No reel elements found');
      return;
    }

    // 🎯 Calculate natural timing for each reel
    const reelTimings = this.calculateNaturalTimings(config);
    
    // 🎰 Start all reels spinning with varied startup
    const startupPromises = this.reelElements.map((reel, index) => 
      this.startReelSpin(reel, index, reelTimings[index])
    );
    
    await Promise.all(startupPromises);
    
    // 🎯 Stop reels with natural variation and anticipation
    const stopPromises = this.reelElements.map((reel, index) => 
      this.stopReelWithTiming(reel, index, finalGrid[index], reelTimings[index], config)
    );
    
    await Promise.all(stopPromises);
  }

  /**
   * 🎯 Calculate natural timing variations for each reel
   */
  private calculateNaturalTimings(config: any): ReelAnimationConfig[] {
    return this.reelElements.map((_, index) => {
      const baseTime = this.BASE_SPIN_TIMES[index];
      const variation = (Math.random() - 0.5) * this.VARIATION_RANGES[index];
      
      let finalTime = baseTime + variation;
      
      // 🎭 Anticipation mode - add dramatic pauses
      if (config.anticipationMode && config.scatterCount === 2) {
        if (index >= 2) { // Last 3 reels get extra delay
          finalTime += this.ANTICIPATION_DELAYS[index - 2];
        }
      }
      
      // 🍀 Luck-based timing adjustments
      if (config.luckScore > 70) {
        finalTime += Math.random() * 300; // Extra suspense for unlucky players
      }
      
      // 🎉 Mega jackpot - extra dramatic timing
      if (config.megaJackpot) {
        finalTime += 500 + (index * 200); // Progressive delay
      }
      
      return {
        reelIndex: index,
        baseSpinTime: finalTime,
        variationRange: this.VARIATION_RANGES[index],
        anticipationMode: config.anticipationMode || false,
        scatterCount: config.scatterCount || 0,
        luckScore: config.luckScore || 50,
      };
    });
  }

  /**
   * 🎰 Start individual reel spinning with natural startup
   */
  private async startReelSpin(
    reel: HTMLElement, 
    index: number, 
    config: ReelAnimationConfig
  ): Promise<void> {
    // 🎯 Startup phase - gradual acceleration
    this.animationStates.set(index, {
      phase: 'startup',
      duration: 200 + Math.random() * 100, // 200-300ms startup
      speed: 0.3 + Math.random() * 0.2, // Slow start
      symbolChangeInterval: 150 + Math.random() * 50
    });
    
    reel.classList.add('reel-spinning');
    
    // 🎯 Gradual acceleration to full speed
    await this.animateStartup(reel, index);
    
    // 🎰 Full spinning phase
    this.animationStates.set(index, {
      phase: 'spinning',
      duration: config.baseSpinTime,
      speed: 1.0,
      symbolChangeInterval: 60 + Math.random() * 20 // Fast spinning
    });
    
    this.startSymbolCycling(reel, index);
  }

  /**
   * 🎯 Animate startup phase with acceleration
   */
  private async animateStartup(reel: HTMLElement, index: number): Promise<void> {
    const state = this.animationStates.get(index)!;
    const steps = 10;
    const stepDuration = state.duration / steps;
    
    for (let step = 0; step < steps; step++) {
      const progress = step / steps;
      const currentSpeed = state.speed + (progress * (1.0 - state.speed));
      
      // Update reel visual speed
      reel.style.setProperty('--spin-speed', `${currentSpeed}`);
      
      await new Promise(resolve => setTimeout(resolve, stepDuration));
    }
  }

  /**
   * 🎰 Start symbol cycling during spin
   */
  private startSymbolCycling(reel: HTMLElement, reelIndex: number): void {
    const state = this.animationStates.get(reelIndex)!;
    const symbols = reel.querySelectorAll('.symbol-container');
    
    const cycleInterval = setInterval(() => {
      const currentState = this.animationStates.get(reelIndex);
      if (!currentState || currentState.phase === 'stopped') {
        clearInterval(cycleInterval);
        return;
      }
      
      // 🎯 Cycle through random symbols from this reel's pool
      symbols.forEach((symbol, rowIndex) => {
        const randomSymbol = this.getRandomSymbolForReel(reelIndex);
        this.updateSymbolDisplay(symbol as HTMLElement, randomSymbol);
      });
      
    }, state.symbolChangeInterval);
  }

  /**
   * 🎯 Stop reel with natural deceleration and timing
   */
  private async stopReelWithTiming(
    reel: HTMLElement,
    index: number,
    finalSymbols: string[],
    config: ReelAnimationConfig,
    globalConfig: any
  ): Promise<void> {
    // Wait for the calculated spin time
    await new Promise(resolve => setTimeout(resolve, config.baseSpinTime));
    
    // 🎭 Special anticipation handling for scatter reels
    if (config.anticipationMode && index >= 2 && config.scatterCount === 2) {
      await this.handleScatterAnticipation(reel, index, finalSymbols, globalConfig);
    } else {
      await this.naturalReelStop(reel, index, finalSymbols);
    }
  }

  /**
   * 🎭 Handle scatter anticipation with dramatic timing
   */
  private async handleScatterAnticipation(
    reel: HTMLElement,
    index: number,
    finalSymbols: string[],
    config: any
  ): Promise<void> {
    // 🎯 Slowdown phase with suspense
    this.animationStates.set(index, {
      phase: 'slowdown',
      duration: 800 + Math.random() * 400, // 800-1200ms slowdown
      speed: 0.3,
      symbolChangeInterval: 200 + Math.random() * 100
    });
    
    // 🎭 Dramatic slowdown
    await this.animateSlowdown(reel, index, 0.8);
    
    // 🎯 Final stop with potential scatter reveal
    const hasScatter = finalSymbols.includes('SCATTER');
    if (hasScatter) {
      // Extra pause before revealing scatter
      await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 300));
    }
    
    await this.finalStop(reel, index, finalSymbols);
  }

  /**
   * 🎰 Natural reel stop without anticipation
   */
  private async naturalReelStop(
    reel: HTMLElement,
    index: number,
    finalSymbols: string[]
  ): Promise<void> {
    // 🎯 Natural slowdown
    this.animationStates.set(index, {
      phase: 'slowdown',
      duration: 300 + Math.random() * 200, // 300-500ms
      speed: 0.5,
      symbolChangeInterval: 120 + Math.random() * 60
    });
    
    await this.animateSlowdown(reel, index, 0.5);
    await this.finalStop(reel, index, finalSymbols);
  }

  /**
   * 🎯 Animate slowdown phase
   */
  private async animateSlowdown(
    reel: HTMLElement,
    index: number,
    targetSpeed: number
  ): Promise<void> {
    const state = this.animationStates.get(index)!;
    const steps = 8;
    const stepDuration = state.duration / steps;
    
    for (let step = 0; step < steps; step++) {
      const progress = step / steps;
      const currentSpeed = 1.0 - (progress * (1.0 - targetSpeed));
      
      reel.style.setProperty('--spin-speed', `${currentSpeed}`);
      
      // Gradually slow down symbol changes
      const newInterval = state.symbolChangeInterval + (progress * 100);
      this.animationStates.set(index, {
        ...state,
        symbolChangeInterval: newInterval
      });
      
      await new Promise(resolve => setTimeout(resolve, stepDuration));
    }
  }

  /**
   * 🎯 Final stop with exact symbol placement
   */
  private async finalStop(
    reel: HTMLElement,
    index: number,
    finalSymbols: string[]
  ): Promise<void> {
    this.animationStates.set(index, {
      phase: 'stopped',
      duration: 0,
      speed: 0,
      symbolChangeInterval: 0
    });
    
    reel.classList.remove('reel-spinning');
    reel.style.removeProperty('--spin-speed');
    
    // 🎯 Set final symbols with slight stagger for natural feel
    const symbols = reel.querySelectorAll('.symbol-container');
    symbols.forEach(async (symbol, rowIndex) => {
      await new Promise(resolve => setTimeout(resolve, rowIndex * 50)); // 50ms stagger
      this.updateSymbolDisplay(symbol as HTMLElement, finalSymbols[rowIndex]);
    });
  }

  /**
   * 🎯 Get random symbol for specific reel (varied distribution)
   */
  private getRandomSymbolForReel(reelIndex: number): string {
    const pool = this.symbolPools[reelIndex] || this.symbolPools[0];
    return pool[Math.floor(Math.random() * pool.length)];
  }

  /**
   * 🎯 Update symbol display with smooth transition
   */
  private updateSymbolDisplay(element: HTMLElement, symbol: string): void {
    const symbolMap: { [key: string]: { emoji: string; color: string } } = {
      '9': { emoji: '9', color: 'text-gray-300' },
      '10': { emoji: '10', color: 'text-gray-300' },
      'J': { emoji: 'J', color: 'text-blue-400' },
      'Q': { emoji: 'Q', color: 'text-purple-400' },
      'K': { emoji: 'K', color: 'text-red-400' },
      'A': { emoji: 'A', color: 'text-casino-gold' },
      'SCATTER': { emoji: '💥', color: 'text-casino-gold' },
      'WILD': { emoji: '🃏', color: 'text-casino-red' },
    };

    const symbolData = symbolMap[symbol] || { emoji: '?', color: 'text-gray-500' };
    
    // Add transition effect
    element.style.opacity = '0.7';
    setTimeout(() => {
      element.innerHTML = `
        <span class="${symbolData.color} text-3xl font-bold transition-all duration-200">
          ${symbolData.emoji}
        </span>
      `;
      element.style.opacity = '1';
    }, 50);
  }

  /**
   * 🎯 Add CSS for natural reel animations
   */
  public static addNaturalReelCSS(): void {
    const style = document.createElement('style');
    style.textContent = `
      .reel-spinning {
        animation: naturalSpin var(--spin-speed, 1)s linear infinite;
      }
      
      @keyframes naturalSpin {
        0% { transform: translateY(0px); }
        25% { transform: translateY(-2px) rotateX(5deg); }
        50% { transform: translateY(0px); }
        75% { transform: translateY(2px) rotateX(-5deg); }
        100% { transform: translateY(0px); }
      }
      
      .symbol-container {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }
      
      .reel-spinning .symbol-container {
        transform: scale(0.98);
        filter: blur(0.5px);
      }
      
      .screen-shake {
        animation: screenShake 0.5s ease-in-out;
      }
      
      @keyframes screenShake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-2px) rotateZ(-0.5deg); }
        75% { transform: translateX(2px) rotateZ(0.5deg); }
      }
    `;
    
    document.head.appendChild(style);
  }
}

// Export singleton instance
export const naturalReelAnimator = new NaturalReelAnimator();

// Add CSS when module loads
if (typeof document !== 'undefined') {
  NaturalReelAnimator.addNaturalReelCSS();
}
