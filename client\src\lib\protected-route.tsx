import { useAuth } from "@/hooks/use-auth";
import { Loader2 } from "lucide-react";
import { Redirect, Route } from "wouter";

export function ProtectedRoute({
  path,
  component: Component,
}: {
  path: string;
  component: () => React.JSX.Element;
}) {
  return (
    <Route path={path}>
      <ProtectedContent Component={Component} />
    </Route>
  );
}

function ProtectedContent({ Component }: { Component: () => React.JSX.Element }) {
  const { user, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-deep-navy via-gray-900 to-purple-900">
        <div className="glass-card p-8 rounded-2xl text-center">
          <Loader2 className="h-12 w-12 animate-spin text-casino-gold mx-auto mb-4" />
          <p className="text-casino-gold font-orbitron text-lg">Loading Vegas Ace Slots...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return <Redirect to="/auth" />;
  }

  return <Component />;
}
