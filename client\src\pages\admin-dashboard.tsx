import { useAuth } from "@/hooks/use-auth";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Users,
  DollarSign,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  XCircle,
  Crown,
  LogOut,
  Settings,
  RefreshCw
} from "lucide-react";
import { useState, useEffect } from "react";
import { useLocation } from "wouter";

interface AdminStats {
  totalUsers: number;
  activeUsers: number;
  pendingDeposits: { count: number; total: string };
  pendingWithdrawals: { count: number; total: string };
  totalBalance: string;
}

interface DepositRequest {
  id: number;
  userId: number;
  amount: string;
  bkashNumber: string;
  transactionId: string;
  status: string;
  createdAt: string;
  adminNotes?: string;
  processedAt?: string;
}

interface WithdrawalRequest {
  id: number;
  userId: number;
  amount: string;
  bkashNumber: string;
  status: string;
  createdAt: string;
  adminNotes?: string;
  processedAt?: string;
}

interface User {
  id: number;
  name: string;
  mobile: string;
  balance: string;
  role: string;
  isActive: boolean;
  joinDate: string;
  lastLogin?: string;
}

export default function AdminDashboard() {
  const { user, logoutMutation } = useAuth();
  const [, setLocation] = useLocation();
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [deposits, setDeposits] = useState<DepositRequest[]>([]);
  const [withdrawals, setWithdrawals] = useState<WithdrawalRequest[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [activeTab, setActiveTab] = useState<'overview' | 'deposits' | 'withdrawals' | 'users'>('overview');
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState<number | null>(null);

  // Check if user is admin
  if (!user || user.role !== 'admin') {
    setLocation('/auth');
    return null;
  }

  const fetchData = async () => {
    try {
      setLoading(true);

      // Fetch admin stats
      const statsRes = await fetch('/api/admin/stats');
      if (statsRes.ok) {
        const statsData = await statsRes.json();
        setStats(statsData);
      }

      // Fetch all deposits
      const depositsRes = await fetch('/api/admin/deposits');
      if (depositsRes.ok) {
        const depositsData = await depositsRes.json();
        setDeposits(depositsData);
      }

      // Fetch all withdrawals
      const withdrawalsRes = await fetch('/api/admin/withdrawals');
      if (withdrawalsRes.ok) {
        const withdrawalsData = await withdrawalsRes.json();
        setWithdrawals(withdrawalsData);
      }

      // Fetch all users
      const usersRes = await fetch('/api/admin/users');
      if (usersRes.ok) {
        const usersData = await usersRes.json();
        setUsers(usersData);
      }
    } catch (error) {
      console.error('Failed to fetch admin data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleLogout = () => {
    logoutMutation.mutate();
  };

  const processDeposit = async (requestId: number, status: 'approved' | 'rejected', notes?: string) => {
    try {
      setProcessing(requestId);
      const response = await fetch(`/api/admin/deposits/${requestId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status, notes })
      });

      if (response.ok) {
        await fetchData(); // Refresh data
        alert(`Deposit ${status} successfully!`);
      } else {
        alert('Failed to process deposit');
      }
    } catch (error) {
      console.error('Error processing deposit:', error);
      alert('Error processing deposit');
    } finally {
      setProcessing(null);
    }
  };

  const processWithdrawal = async (requestId: number, status: 'completed' | 'rejected', notes?: string) => {
    try {
      setProcessing(requestId);
      const response = await fetch(`/api/admin/withdrawals/${requestId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status, notes })
      });

      if (response.ok) {
        await fetchData(); // Refresh data
        alert(`Withdrawal ${status} successfully!`);
      } else {
        alert('Failed to process withdrawal');
      }
    } catch (error) {
      console.error('Error processing withdrawal:', error);
      alert('Error processing withdrawal');
    } finally {
      setProcessing(null);
    }
  };

  const toggleUserStatus = async (userId: number, isActive: boolean) => {
    try {
      const response = await fetch(`/api/admin/users/${userId}/status`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isActive })
      });

      if (response.ok) {
        await fetchData(); // Refresh data
        alert(`User ${isActive ? 'activated' : 'blocked'} successfully!`);
      } else {
        alert('Failed to update user status');
      }
    } catch (error) {
      console.error('Error updating user status:', error);
      alert('Error updating user status');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-deep-navy via-gray-900 to-purple-900 relative overflow-hidden">
      {/* Animated Background */}
      <div className="fixed inset-0 opacity-20 pointer-events-none">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-casino-purple rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-casino-red rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 w-96 h-96 bg-casino-gold rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>

      {/* Header */}
      <header className="relative z-10 glass-card border-b border-casino-gold/30 p-6">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-r from-casino-red to-casino-purple rounded-xl flex items-center justify-center">
              <Crown className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-orbitron font-bold text-transparent bg-clip-text bg-gradient-to-r from-casino-gold via-casino-red to-casino-purple">
                SUPER ADMIN PANEL
              </h1>
              <p className="text-sm text-gray-300">Vegas Ace Slots Management</p>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <Button
              onClick={fetchData}
              variant="outline"
              size="sm"
              className="border-casino-gold/30 text-casino-gold hover:bg-casino-gold/10"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>

            <div className="text-right">
              <p className="font-semibold text-white">{user.name}</p>
              <p className="text-xs text-casino-red font-bold">SUPER ADMIN</p>
            </div>

            <Button
              onClick={handleLogout}
              variant="outline"
              size="sm"
              className="border-red-500/30 text-red-400 hover:bg-red-500/10"
            >
              <LogOut className="w-4 h-4 mr-2" />
              Logout
            </Button>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <div className="relative z-10 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex space-x-1 bg-black/20 p-1 rounded-lg border border-casino-gold/20">
            {[
              { id: 'overview', label: 'Overview', icon: TrendingUp },
              { id: 'deposits', label: 'Deposits', icon: DollarSign },
              { id: 'withdrawals', label: 'Withdrawals', icon: AlertCircle },
              { id: 'users', label: 'Users', icon: Users }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-md transition-all ${
                  activeTab === tab.id
                    ? 'bg-casino-gold text-black font-bold'
                    : 'text-gray-300 hover:text-white hover:bg-white/5'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <main className="relative z-10 p-6">
        <div className="max-w-7xl mx-auto">
          {loading ? (
            <div className="flex items-center justify-center py-20">
              <div className="text-center">
                <RefreshCw className="w-8 h-8 text-casino-gold animate-spin mx-auto mb-4" />
                <p className="text-gray-300">Loading admin data...</p>
              </div>
            </div>
          ) : (
            <>
              {activeTab === 'overview' && (
                <div className="space-y-6">
                  {/* Stats Cards */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <Card className="glass-card border-casino-gold/30">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium text-gray-300 flex items-center">
                          <Users className="w-4 h-4 mr-2 text-casino-gold" />
                          Total Users
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-white">{stats?.totalUsers || 0}</div>
                        <p className="text-xs text-green-400">
                          {stats?.activeUsers || 0} active
                        </p>
                      </CardContent>
                    </Card>

                    <Card className="glass-card border-casino-purple/30">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium text-gray-300 flex items-center">
                          <DollarSign className="w-4 h-4 mr-2 text-casino-purple" />
                          Pending Deposits
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-white">{stats?.pendingDeposits.count || 0}</div>
                        <p className="text-xs text-casino-purple">
                          ৳{parseFloat(stats?.pendingDeposits.total || "0").toFixed(2)}
                        </p>
                      </CardContent>
                    </Card>

                    <Card className="glass-card border-casino-red/30">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium text-gray-300 flex items-center">
                          <AlertCircle className="w-4 h-4 mr-2 text-casino-red" />
                          Pending Withdrawals
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-white">{stats?.pendingWithdrawals.count || 0}</div>
                        <p className="text-xs text-casino-red">
                          ৳{parseFloat(stats?.pendingWithdrawals.total || "0").toFixed(2)}
                        </p>
                      </CardContent>
                    </Card>

                    <Card className="glass-card border-green-500/30">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium text-gray-300 flex items-center">
                          <TrendingUp className="w-4 h-4 mr-2 text-green-400" />
                          Total Balance
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-white">
                          ৳{parseFloat(stats?.totalBalance || "0").toFixed(2)}
                        </div>
                        <p className="text-xs text-green-400">Platform liquidity</p>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Recent Activity */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Recent Deposits */}
                    <Card className="glass-card border-casino-gold/30">
                      <CardHeader>
                        <CardTitle className="text-casino-gold flex items-center">
                          <DollarSign className="w-5 h-5 mr-2" />
                          Recent Deposit Requests
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          {deposits.slice(0, 5).map((deposit) => (
                            <div key={deposit.id} className="flex items-center justify-between p-3 bg-black/20 rounded-lg">
                              <div>
                                <p className="text-white font-medium">৳{parseFloat(deposit.amount).toFixed(2)}</p>
                                <p className="text-xs text-gray-400">User ID: {deposit.userId}</p>
                              </div>
                              <Badge variant="outline" className="border-yellow-500 text-yellow-500">
                                Pending
                              </Badge>
                            </div>
                          ))}
                          {deposits.length === 0 && (
                            <p className="text-gray-400 text-center py-4">No pending deposits</p>
                          )}
                        </div>
                      </CardContent>
                    </Card>

                    {/* Recent Withdrawals */}
                    <Card className="glass-card border-casino-red/30">
                      <CardHeader>
                        <CardTitle className="text-casino-red flex items-center">
                          <AlertCircle className="w-5 h-5 mr-2" />
                          Recent Withdrawal Requests
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          {withdrawals.slice(0, 5).map((withdrawal) => (
                            <div key={withdrawal.id} className="flex items-center justify-between p-3 bg-black/20 rounded-lg">
                              <div>
                                <p className="text-white font-medium">৳{parseFloat(withdrawal.amount).toFixed(2)}</p>
                                <p className="text-xs text-gray-400">User ID: {withdrawal.userId}</p>
                              </div>
                              <Badge variant="outline" className="border-red-500 text-red-500">
                                Pending
                              </Badge>
                            </div>
                          ))}
                          {withdrawals.length === 0 && (
                            <p className="text-gray-400 text-center py-4">No pending withdrawals</p>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              )}

              {/* Deposits Management */}
              {activeTab === 'deposits' && (
                <div className="space-y-6">
                  <div className="flex justify-between items-center">
                    <h2 className="text-2xl font-bold text-casino-gold">Deposit Management</h2>
                    <div className="flex space-x-2">
                      <Badge variant="outline" className="border-yellow-500 text-yellow-500">
                        {deposits.filter(d => d.status === 'pending').length} Pending
                      </Badge>
                      <Badge variant="outline" className="border-green-500 text-green-500">
                        {deposits.filter(d => d.status === 'approved').length} Approved
                      </Badge>
                      <Badge variant="outline" className="border-red-500 text-red-500">
                        {deposits.filter(d => d.status === 'rejected').length} Rejected
                      </Badge>
                    </div>
                  </div>

                  <div className="grid gap-4">
                    {deposits.map((deposit) => (
                      <Card key={deposit.id} className="glass-card border-casino-gold/30">
                        <CardContent className="p-6">
                          <div className="flex justify-between items-start">
                            <div className="space-y-2">
                              <div className="flex items-center space-x-4">
                                <h3 className="text-lg font-bold text-white">৳{parseFloat(deposit.amount).toFixed(2)}</h3>
                                <Badge
                                  variant="outline"
                                  className={
                                    deposit.status === 'pending' ? 'border-yellow-500 text-yellow-500' :
                                    deposit.status === 'approved' ? 'border-green-500 text-green-500' :
                                    'border-red-500 text-red-500'
                                  }
                                >
                                  {deposit.status.toUpperCase()}
                                </Badge>
                              </div>
                              <p className="text-gray-300">User ID: {deposit.userId}</p>
                              <p className="text-gray-300">Bkash: {deposit.bkashNumber}</p>
                              <p className="text-gray-300">Transaction ID: {deposit.transactionId}</p>
                              <p className="text-gray-400 text-sm">
                                Submitted: {new Date(deposit.createdAt).toLocaleString()}
                              </p>
                            </div>

                            {deposit.status === 'pending' && (
                              <div className="flex space-x-2">
                                <Button
                                  onClick={() => processDeposit(deposit.id, 'approved')}
                                  disabled={processing === deposit.id}
                                  className="bg-green-600 hover:bg-green-700 text-white"
                                  size="sm"
                                >
                                  <CheckCircle className="w-4 h-4 mr-1" />
                                  Approve
                                </Button>
                                <Button
                                  onClick={() => processDeposit(deposit.id, 'rejected', 'Invalid transaction')}
                                  disabled={processing === deposit.id}
                                  variant="destructive"
                                  size="sm"
                                >
                                  <XCircle className="w-4 h-4 mr-1" />
                                  Reject
                                </Button>
                              </div>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                    {deposits.length === 0 && (
                      <div className="text-center py-12">
                        <p className="text-gray-400">No deposit requests found</p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Withdrawals Management */}
              {activeTab === 'withdrawals' && (
                <div className="space-y-6">
                  <div className="flex justify-between items-center">
                    <h2 className="text-2xl font-bold text-casino-red">Withdrawal Management</h2>
                    <div className="flex space-x-2">
                      <Badge variant="outline" className="border-yellow-500 text-yellow-500">
                        {withdrawals.filter(w => w.status === 'pending').length} Pending
                      </Badge>
                      <Badge variant="outline" className="border-green-500 text-green-500">
                        {withdrawals.filter(w => w.status === 'completed').length} Completed
                      </Badge>
                      <Badge variant="outline" className="border-red-500 text-red-500">
                        {withdrawals.filter(w => w.status === 'rejected').length} Rejected
                      </Badge>
                    </div>
                  </div>

                  <div className="grid gap-4">
                    {withdrawals.map((withdrawal) => (
                      <Card key={withdrawal.id} className="glass-card border-casino-red/30">
                        <CardContent className="p-6">
                          <div className="flex justify-between items-start">
                            <div className="space-y-2">
                              <div className="flex items-center space-x-4">
                                <h3 className="text-lg font-bold text-white">৳{parseFloat(withdrawal.amount).toFixed(2)}</h3>
                                <Badge
                                  variant="outline"
                                  className={
                                    withdrawal.status === 'pending' ? 'border-yellow-500 text-yellow-500' :
                                    withdrawal.status === 'completed' ? 'border-green-500 text-green-500' :
                                    'border-red-500 text-red-500'
                                  }
                                >
                                  {withdrawal.status.toUpperCase()}
                                </Badge>
                              </div>
                              <p className="text-gray-300">User ID: {withdrawal.userId}</p>
                              <p className="text-gray-300">Bkash: {withdrawal.bkashNumber}</p>
                              <p className="text-gray-400 text-sm">
                                Requested: {new Date(withdrawal.createdAt).toLocaleString()}
                              </p>
                              {withdrawal.adminNotes && (
                                <p className="text-yellow-400 text-sm">Notes: {withdrawal.adminNotes}</p>
                              )}
                            </div>

                            {withdrawal.status === 'pending' && (
                              <div className="flex space-x-2">
                                <Button
                                  onClick={() => processWithdrawal(withdrawal.id, 'completed')}
                                  disabled={processing === withdrawal.id}
                                  className="bg-green-600 hover:bg-green-700 text-white"
                                  size="sm"
                                >
                                  <CheckCircle className="w-4 h-4 mr-1" />
                                  Complete
                                </Button>
                                <Button
                                  onClick={() => processWithdrawal(withdrawal.id, 'rejected', 'Unable to process')}
                                  disabled={processing === withdrawal.id}
                                  variant="destructive"
                                  size="sm"
                                >
                                  <XCircle className="w-4 h-4 mr-1" />
                                  Reject
                                </Button>
                              </div>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                    {withdrawals.length === 0 && (
                      <div className="text-center py-12">
                        <p className="text-gray-400">No withdrawal requests found</p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Users Management */}
              {activeTab === 'users' && (
                <div className="space-y-6">
                  <div className="flex justify-between items-center">
                    <h2 className="text-2xl font-bold text-casino-purple">User Management</h2>
                    <div className="flex space-x-2">
                      <Badge variant="outline" className="border-green-500 text-green-500">
                        {users.filter(u => u.isActive).length} Active
                      </Badge>
                      <Badge variant="outline" className="border-red-500 text-red-500">
                        {users.filter(u => !u.isActive).length} Blocked
                      </Badge>
                    </div>
                  </div>

                  <div className="grid gap-4">
                    {users.map((user) => (
                      <Card key={user.id} className="glass-card border-casino-purple/30">
                        <CardContent className="p-6">
                          <div className="flex justify-between items-start">
                            <div className="space-y-2">
                              <div className="flex items-center space-x-4">
                                <h3 className="text-lg font-bold text-white">{user.name}</h3>
                                <Badge
                                  variant="outline"
                                  className={user.isActive ? 'border-green-500 text-green-500' : 'border-red-500 text-red-500'}
                                >
                                  {user.isActive ? 'ACTIVE' : 'BLOCKED'}
                                </Badge>
                                {user.role === 'admin' && (
                                  <Badge variant="outline" className="border-casino-gold text-casino-gold">
                                    ADMIN
                                  </Badge>
                                )}
                              </div>
                              <p className="text-gray-300">ID: {user.id}</p>
                              <p className="text-gray-300">Mobile: {user.mobile}</p>
                              <p className="text-gray-300">Balance: ৳{parseFloat(user.balance).toFixed(2)}</p>
                              <p className="text-gray-400 text-sm">
                                Joined: {new Date(user.joinDate).toLocaleDateString()}
                              </p>
                              {user.lastLogin && (
                                <p className="text-gray-400 text-sm">
                                  Last Login: {new Date(user.lastLogin).toLocaleString()}
                                </p>
                              )}
                            </div>

                            {user.role !== 'admin' && (
                              <div className="flex space-x-2">
                                <Button
                                  onClick={() => toggleUserStatus(user.id, !user.isActive)}
                                  variant={user.isActive ? "destructive" : "default"}
                                  size="sm"
                                  className={user.isActive ? "" : "bg-green-600 hover:bg-green-700 text-white"}
                                >
                                  {user.isActive ? (
                                    <>
                                      <XCircle className="w-4 h-4 mr-1" />
                                      Block User
                                    </>
                                  ) : (
                                    <>
                                      <CheckCircle className="w-4 h-4 mr-1" />
                                      Activate User
                                    </>
                                  )}
                                </Button>
                              </div>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                    {users.length === 0 && (
                      <div className="text-center py-12">
                        <p className="text-gray-400">No users found</p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </main>
    </div>
  );
}
