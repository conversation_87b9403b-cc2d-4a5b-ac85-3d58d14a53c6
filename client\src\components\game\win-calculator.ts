/**
 * WinCalculator - Advanced win detection and calculation system
 * Implements 1024 ways-to-win logic with cascading mechanics
 */

import { symbolManager } from './symbol-manager';

export interface WinPosition {
  reel: number;
  row: number;
}

export interface WinLine {
  symbol: string;
  positions: WinPosition[];
  count: number;
  payout: number;
  type: 'ways' | 'scatter' | 'line';
  multiplier?: number;
}

export interface WinResult {
  wins: WinLine[];
  totalPayout: number;
  scatterCount: number;
  hasBonus: boolean;
  hasFreeSpins: boolean;
  cascadePositions?: WinPosition[];
}

export class WinCalculator {
  private readonly REELS = 5;
  private readonly ROWS = 4;
  private readonly MIN_WIN_LENGTH = 3;

  constructor() {}

  /**
   * Calculate all wins in the symbol grid
   */
  public calculateWins(
    symbolGrid: string[][],
    betAmount: number,
    multiplier: number = 1
  ): WinResult {
    if (!this.validateGrid(symbolGrid)) {
      throw new Error('Invalid symbol grid provided');
    }

    const wins: WinLine[] = [];
    const processedPositions = new Set<string>();

    // Calculate ways-to-win
    const waysWins = this.calculateWaysToWin(symbolGrid, processedPositions);
    wins.push(...waysWins);

    // Calculate scatter wins
    const scatterWins = this.calculateScatterWins(symbolGrid);
    wins.push(...scatterWins);

    // Apply bet amount and multiplier to payouts
    const adjustedWins = wins.map(win => ({
      ...win,
      payout: win.payout * betAmount * multiplier * (win.multiplier || 1)
    }));

    // Calculate total payout
    const totalPayout = adjustedWins.reduce((sum, win) => sum + win.payout, 0);

    // Check for bonus features
    const scatterCount = this.countScatters(symbolGrid);
    const hasBonus = scatterCount >= 3;
    const hasFreeSpins = scatterCount >= 3;

    return {
      wins: adjustedWins,
      totalPayout,
      scatterCount,
      hasBonus,
      hasFreeSpins,
    };
  }

  /**
   * Calculate ways-to-win (1024 ways)
   */
  private calculateWaysToWin(
    symbolGrid: string[][],
    processedPositions: Set<string>
  ): WinLine[] {
    const wins: WinLine[] = [];

    // Check each starting position in the leftmost reel
    for (let startRow = 0; startRow < this.ROWS; startRow++) {
      const startSymbol = symbolGrid[0][startRow];
      
      if (!startSymbol || symbolManager.isScatter(startSymbol)) {
        continue; // Skip empty positions and scatters
      }

      // Find the longest winning path from this position
      const winPath = this.findLongestWinPath(
        symbolGrid,
        startSymbol,
        0,
        startRow,
        processedPositions
      );

      if (winPath.length >= this.MIN_WIN_LENGTH) {
        const basePayout = symbolManager.getSymbolPayout(startSymbol, winPath.length);
        
        if (basePayout > 0) {
          wins.push({
            symbol: startSymbol,
            positions: winPath,
            count: winPath.length,
            payout: basePayout,
            type: 'ways',
            multiplier: symbolManager.getSymbolMultiplier(startSymbol),
          });

          // Mark positions as processed to avoid overlapping wins
          winPath.forEach(pos => {
            processedPositions.add(`${pos.reel}-${pos.row}`);
          });
        }
      }
    }

    return wins;
  }

  /**
   * Find the longest winning path using dynamic programming
   */
  private findLongestWinPath(
    symbolGrid: string[][],
    targetSymbol: string,
    startReel: number,
    startRow: number,
    processedPositions: Set<string>
  ): WinPosition[] {
    const memo = new Map<string, WinPosition[]>();
    
    const findPath = (reel: number, currentPath: WinPosition[]): WinPosition[] => {
      // Base case: reached the end of reels
      if (reel >= this.REELS) {
        return currentPath;
      }

      // Create memoization key
      const key = `${reel}-${currentPath.length}-${targetSymbol}`;
      if (memo.has(key)) {
        return memo.get(key)!;
      }

      let bestPath = currentPath;
      let foundMatch = false;

      // Check all positions in current reel
      for (let row = 0; row < this.ROWS; row++) {
        const symbol = symbolGrid[reel][row];
        const posKey = `${reel}-${row}`;

        // Skip if position already processed or no symbol
        if (!symbol || processedPositions.has(posKey)) {
          continue;
        }

        // Check if symbols match (including wild substitution)
        if (this.symbolsMatch(targetSymbol, symbol)) {
          foundMatch = true;
          const newPath = [...currentPath, { reel, row }];
          
          // Recursively find the rest of the path
          const completePath = findPath(reel + 1, newPath);
          
          // Keep the longest path
          if (completePath.length > bestPath.length) {
            bestPath = completePath;
          }
        }
      }

      // If no match found in current reel, return current path
      if (!foundMatch) {
        bestPath = currentPath;
      }

      memo.set(key, bestPath);
      return bestPath;
    };

    return findPath(startReel, [{ reel: startReel, row: startRow }]);
  }

  /**
   * Calculate scatter wins (can appear anywhere)
   */
  private calculateScatterWins(symbolGrid: string[][]): WinLine[] {
    const scatterPositions: WinPosition[] = [];

    // Find all scatter positions
    for (let reel = 0; reel < this.REELS; reel++) {
      for (let row = 0; row < this.ROWS; row++) {
        if (symbolManager.isScatter(symbolGrid[reel][row])) {
          scatterPositions.push({ reel, row });
        }
      }
    }

    // Need at least 3 scatters for a win
    if (scatterPositions.length >= 3) {
      const scatterSymbol = 'SCATTER';
      const payout = symbolManager.getSymbolPayout(scatterSymbol, scatterPositions.length);
      
      return [{
        symbol: scatterSymbol,
        positions: scatterPositions,
        count: scatterPositions.length,
        payout,
        type: 'scatter',
      }];
    }

    return [];
  }

  /**
   * Check if two symbols match (including wild substitution)
   */
  private symbolsMatch(symbol1: string, symbol2: string): boolean {
    return symbolManager.symbolsMatch(symbol1, symbol2);
  }

  /**
   * Count total scatters in the grid
   */
  private countScatters(symbolGrid: string[][]): number {
    let count = 0;
    
    for (let reel = 0; reel < this.REELS; reel++) {
      for (let row = 0; row < this.ROWS; row++) {
        if (symbolManager.isScatter(symbolGrid[reel][row])) {
          count++;
        }
      }
    }
    
    return count;
  }

  /**
   * Validate symbol grid structure
   */
  private validateGrid(symbolGrid: string[][]): boolean {
    if (!Array.isArray(symbolGrid)) return false;
    if (symbolGrid.length !== this.REELS) return false;

    for (let reel = 0; reel < this.REELS; reel++) {
      if (!Array.isArray(symbolGrid[reel])) return false;
      if (symbolGrid[reel].length !== this.ROWS) return false;
    }

    return true;
  }

  /**
   * Calculate hit frequency (percentage of spins that result in a win)
   */
  public calculateHitFrequency(symbolGrid: string[][]): number {
    const result = this.calculateWins(symbolGrid, 1, 1);
    return result.wins.length > 0 ? 1 : 0; // Simplified for single calculation
  }

  /**
   * Get win statistics for analysis
   */
  public getWinStats(wins: WinLine[]): {
    totalWins: number;
    waysWins: number;
    scatterWins: number;
    biggestWin: number;
    averageWin: number;
    winTypes: { [type: string]: number };
  } {
    if (wins.length === 0) {
      return {
        totalWins: 0,
        waysWins: 0,
        scatterWins: 0,
        biggestWin: 0,
        averageWin: 0,
        winTypes: {},
      };
    }

    const winTypes: { [type: string]: number } = {};
    let totalPayout = 0;
    let biggestWin = 0;
    let waysWins = 0;
    let scatterWins = 0;

    wins.forEach(win => {
      winTypes[win.type] = (winTypes[win.type] || 0) + 1;
      totalPayout += win.payout;
      
      if (win.payout > biggestWin) {
        biggestWin = win.payout;
      }
      
      if (win.type === 'ways') waysWins++;
      if (win.type === 'scatter') scatterWins++;
    });

    return {
      totalWins: wins.length,
      waysWins,
      scatterWins,
      biggestWin,
      averageWin: totalPayout / wins.length,
      winTypes,
    };
  }

  /**
   * Check for cascading opportunities (symbols that should be removed)
   */
  public getCascadePositions(wins: WinLine[]): WinPosition[] {
    const positions: WinPosition[] = [];
    
    wins.forEach(win => {
      win.positions.forEach(pos => {
        // Avoid duplicate positions
        if (!positions.some(p => p.reel === pos.reel && p.row === pos.row)) {
          positions.push(pos);
        }
      });
    });

    return positions;
  }

  /**
   * Calculate potential multiplier escalation
   */
  public calculateMultiplierEscalation(consecutiveWins: number): number {
    // Progressive multiplier: 1x, 2x, 3x, 5x, 10x
    const multipliers = [1, 2, 3, 5, 10];
    const index = Math.min(consecutiveWins, multipliers.length - 1);
    return multipliers[index];
  }

  /**
   * Generate win summary for display
   */
  public generateWinSummary(result: WinResult): string {
    if (result.wins.length === 0) {
      return "No wins this spin";
    }

    const summaryParts: string[] = [];
    
    if (result.wins.some(w => w.type === 'ways')) {
      const waysWins = result.wins.filter(w => w.type === 'ways').length;
      summaryParts.push(`${waysWins} way${waysWins > 1 ? 's' : ''} win`);
    }
    
    if (result.scatterCount >= 3) {
      summaryParts.push(`${result.scatterCount} scatters`);
    }
    
    if (result.hasBonus) {
      summaryParts.push("BONUS TRIGGERED!");
    }

    return summaryParts.join(" + ");
  }
}

// Export singleton instance
export const winCalculator = new WinCalculator();
