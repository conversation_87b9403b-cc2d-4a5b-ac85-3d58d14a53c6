import { db } from "../server/db";
import { users } from "../shared/schema";
import { scrypt, randomBytes } from "crypto";
import { promisify } from "util";
import { eq } from "drizzle-orm";

const scryptAsync = promisify(scrypt);

async function hashPassword(password: string) {
  const salt = randomBytes(16).toString("hex");
  const buf = (await scryptAsync(password, salt, 64)) as Buffer;
  return `${buf.toString("hex")}.${salt}`;
}

async function createAdminUser() {
  try {
    console.log("🔐 Creating Super Admin User...");

    const adminMobile = "01700000000"; // Super Admin mobile
    const adminPassword = "admin123"; // Super Admin password
    const adminName = "Super Admin";

    // Check if admin already exists
    const existingAdmin = await db
      .select()
      .from(users)
      .where(eq(users.mobile, adminMobile))
      .limit(1);

    if (existingAdmin.length > 0) {
      console.log("⚠️  Super Admin already exists!");
      console.log("📱 Mobile:", adminMobile);
      console.log("🔑 Password: admin123");
      return;
    }

    // Hash the password
    const hashedPassword = await hashPassword(adminPassword);

    // Create admin user
    const [admin] = await db
      .insert(users)
      .values({
        name: adminName,
        mobile: adminMobile,
        password: hashedPassword,
        balance: "999999.00", // Give admin a large balance
        role: "admin",
        isActive: true,
      })
      .returning();

    console.log("✅ Super Admin created successfully!");
    console.log("👤 Name:", admin.name);
    console.log("📱 Mobile:", admin.mobile);
    console.log("🔑 Password: admin123");
    console.log("💰 Balance: $999,999.00");
    console.log("🎯 Role: admin");
    console.log("");
    console.log("🚀 You can now login with these credentials:");
    console.log("   Mobile: 01700000000");
    console.log("   Password: admin123");
    console.log("");
    console.log("🔒 This will redirect you to the admin dashboard at /admin");

  } catch (error) {
    console.error("❌ Error creating admin user:", error);
  } finally {
    process.exit(0);
  }
}

createAdminUser();
