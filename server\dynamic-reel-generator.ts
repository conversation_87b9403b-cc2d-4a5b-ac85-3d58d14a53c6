/**
 * 🎰 DYNAMIC REEL GENERATOR
 * Creates varied reel patterns and controls scatter frequency
 * Prevents repetitive patterns and manages symbol distribution
 */

interface ReelSet {
  id: string;
  name: string;
  symbols: string[][];
  scatterFrequency: number;
  wildFrequency: number;
  variance: 'low' | 'medium' | 'high';
}

interface GenerationParams {
  shouldWin: boolean;
  winType: 'tiny' | 'small' | 'medium' | 'big' | 'mega';
  forceScatter: boolean;
  targetSymbol?: string;
  avoidScatters?: boolean;
}

export class DynamicReelGenerator {
  private reelSets: ReelSet[] = [];
  private lastUsedSet: string = '';
  private scatterCooldown: Map<number, number> = new Map(); // userId -> last scatter spin
  private readonly SCATTER_MIN_INTERVAL = 15; // Minimum spins between scatters

  constructor() {
    this.initializeReelSets();
  }

  /**
   * 🎰 Initialize different reel sets for variety
   */
  private initializeReelSets(): void {
    // Low variance reel set (frequent small wins)
    this.reelSets.push({
      id: 'low_variance',
      name: 'Steady Play',
      symbols: this.createReelSet({
        '9': 25, '10': 22, 'J': 18, 'Q': 15, 'K': 12, 'A': 8,
        'WILD': 2, 'SCATTER': 1
      }),
      scatterFrequency: 0.02,
      wildFrequency: 0.05,
      variance: 'low'
    });

    // Medium variance reel set (balanced)
    this.reelSets.push({
      id: 'medium_variance',
      name: 'Balanced Play',
      symbols: this.createReelSet({
        '9': 20, '10': 18, 'J': 16, 'Q': 14, 'K': 12, 'A': 10,
        'WILD': 3, 'SCATTER': 2
      }),
      scatterFrequency: 0.03,
      wildFrequency: 0.06,
      variance: 'medium'
    });

    // High variance reel set (big wins, more volatility)
    this.reelSets.push({
      id: 'high_variance',
      name: 'High Stakes',
      symbols: this.createReelSet({
        '9': 15, '10': 15, 'J': 15, 'Q': 15, 'K': 15, 'A': 15,
        'WILD': 4, 'SCATTER': 3
      }),
      scatterFrequency: 0.04,
      wildFrequency: 0.08,
      variance: 'high'
    });

    // Bonus-heavy reel set (for retention)
    this.reelSets.push({
      id: 'bonus_heavy',
      name: 'Bonus Hunter',
      symbols: this.createReelSet({
        '9': 18, '10': 16, 'J': 14, 'Q': 12, 'K': 10, 'A': 8,
        'WILD': 5, 'SCATTER': 4
      }),
      scatterFrequency: 0.06,
      wildFrequency: 0.10,
      variance: 'high'
    });
  }

  /**
   * 🎲 Create a reel set with specified symbol weights
   */
  private createReelSet(weights: { [symbol: string]: number }): string[][] {
    const reels: string[][] = [];
    
    for (let reelIndex = 0; reelIndex < 5; reelIndex++) {
      const reel: string[] = [];
      
      // Create weighted symbol pool
      const symbolPool: string[] = [];
      for (const [symbol, weight] of Object.entries(weights)) {
        for (let i = 0; i < weight; i++) {
          symbolPool.push(symbol);
        }
      }
      
      // Generate reel strip (longer for more variety)
      for (let i = 0; i < 100; i++) {
        const randomIndex = Math.floor(Math.random() * symbolPool.length);
        reel.push(symbolPool[randomIndex]);
      }
      
      reels.push(reel);
    }
    
    return reels;
  }

  /**
   * 🎯 Select optimal reel set based on user behavior and game state
   */
  public selectReelSet(
    userLevel: 'new' | 'retained' | 'vip' | 'whale',
    retentionRisk: 'low' | 'medium' | 'high',
    consecutiveLosses: number
  ): ReelSet {
    let selectedSet: ReelSet;

    // High retention risk - use bonus-heavy set
    if (retentionRisk === 'high' || consecutiveLosses >= 10) {
      selectedSet = this.reelSets.find(set => set.id === 'bonus_heavy')!;
    }
    // New users - use low variance for steady experience
    else if (userLevel === 'new') {
      selectedSet = this.reelSets.find(set => set.id === 'low_variance')!;
    }
    // VIP/Whale users - use high variance for excitement
    else if (userLevel === 'vip' || userLevel === 'whale') {
      selectedSet = this.reelSets.find(set => set.id === 'high_variance')!;
    }
    // Default to medium variance
    else {
      selectedSet = this.reelSets.find(set => set.id === 'medium_variance')!;
    }

    // Avoid using the same set twice in a row for variety
    if (selectedSet.id === this.lastUsedSet && this.reelSets.length > 1) {
      const alternatives = this.reelSets.filter(set => set.id !== this.lastUsedSet);
      selectedSet = alternatives[Math.floor(Math.random() * alternatives.length)];
    }

    this.lastUsedSet = selectedSet.id;
    return selectedSet;
  }

  /**
   * 🎰 Generate dynamic grid based on parameters
   */
  public generateGrid(
    userId: number,
    reelSet: ReelSet,
    params: GenerationParams,
    rng: any
  ): string[][] {
    const grid: string[][] = [];

    // Check scatter cooldown
    const lastScatter = this.scatterCooldown.get(userId) || 0;
    const currentSpin = Date.now();
    const scatterAllowed = (currentSpin - lastScatter) > (this.SCATTER_MIN_INTERVAL * 3000); // 3 sec per spin

    for (let reel = 0; reel < 5; reel++) {
      grid[reel] = [];
      const reelStrip = reelSet.symbols[reel];
      
      for (let row = 0; row < 4; row++) {
        let symbol: string;

        // Force winning patterns if needed
        if (params.shouldWin && this.shouldPlaceWinningSymbol(reel, row, params)) {
          symbol = this.selectWinningSymbol(params, rng);
        }
        // Force scatter if requested and allowed
        else if (params.forceScatter && scatterAllowed && this.shouldPlaceScatter(reel, row, rng)) {
          symbol = 'SCATTER';
          this.scatterCooldown.set(userId, currentSpin);
        }
        // Avoid scatters if requested
        else if (params.avoidScatters) {
          symbol = this.selectNonScatterSymbol(reelStrip, rng);
        }
        // Normal symbol selection
        else {
          symbol = this.selectNormalSymbol(reelStrip, reelSet, rng);
        }

        grid[reel][row] = symbol;
      }
    }

    return this.postProcessGrid(grid, params, rng);
  }

  /**
   * 🎯 Determine if this position should have a winning symbol
   */
  private shouldPlaceWinningSymbol(reel: number, row: number, params: GenerationParams): boolean {
    // Place winning symbols on first 3 reels for guaranteed wins
    if (params.shouldWin && reel < 3) {
      // Higher chance on top rows for visibility
      const rowWeight = row === 0 ? 0.4 : row === 1 ? 0.3 : 0.2;
      return Math.random() < rowWeight;
    }
    return false;
  }

  /**
   * 🎲 Select winning symbol based on win type
   */
  private selectWinningSymbol(params: GenerationParams, rng: any): string {
    if (params.targetSymbol) {
      return params.targetSymbol;
    }

    // Select symbol based on win type
    switch (params.winType) {
      case 'mega':
        return Math.random() < 0.3 ? 'WILD' : 'A';
      case 'big':
        return Math.random() < 0.2 ? 'WILD' : (Math.random() < 0.5 ? 'A' : 'K');
      case 'medium':
        const mediumSymbols = ['A', 'K', 'Q', 'J'];
        return mediumSymbols[Math.floor(rng.next() * mediumSymbols.length)];
      case 'small':
      case 'tiny':
      default:
        const smallSymbols = ['Q', 'J', '10', '9'];
        return smallSymbols[Math.floor(rng.next() * smallSymbols.length)];
    }
  }

  /**
   * 🎰 Determine if this position should have a scatter
   */
  private shouldPlaceScatter(reel: number, row: number, rng: any): boolean {
    // Distribute scatters across different reels and rows
    const scatterChance = 0.15; // 15% chance per position when forcing
    return rng.next() < scatterChance;
  }

  /**
   * 🚫 Select non-scatter symbol
   */
  private selectNonScatterSymbol(reelStrip: string[], rng: any): string {
    const nonScatterSymbols = reelStrip.filter(symbol => symbol !== 'SCATTER');
    return nonScatterSymbols[Math.floor(rng.next() * nonScatterSymbols.length)];
  }

  /**
   * 🎲 Select normal symbol with frequency control
   */
  private selectNormalSymbol(reelStrip: string[], reelSet: ReelSet, rng: any): string {
    // Apply frequency adjustments
    let symbol = reelStrip[Math.floor(rng.next() * reelStrip.length)];
    
    // Reduce scatter frequency if not forcing
    if (symbol === 'SCATTER' && rng.next() > reelSet.scatterFrequency) {
      symbol = this.selectNonScatterSymbol(reelStrip, rng);
    }
    
    // Reduce wild frequency slightly for balance
    if (symbol === 'WILD' && rng.next() > reelSet.wildFrequency) {
      const regularSymbols = reelStrip.filter(s => s !== 'WILD' && s !== 'SCATTER');
      symbol = regularSymbols[Math.floor(rng.next() * regularSymbols.length)];
    }
    
    return symbol;
  }

  /**
   * 🔧 Post-process grid for final adjustments
   */
  private postProcessGrid(grid: string[][], params: GenerationParams, rng: any): string[][] {
    // Ensure no accidental wins when not intended
    if (!params.shouldWin && !params.forceScatter) {
      this.removeAccidentalWins(grid, rng);
    }
    
    // Ensure scatters are properly distributed
    if (params.forceScatter) {
      this.ensureScatterDistribution(grid, rng);
    }
    
    return grid;
  }

  /**
   * 🚫 Remove accidental winning combinations
   */
  private removeAccidentalWins(grid: string[][], rng: any): void {
    // Check for 3+ consecutive matching symbols and break them
    for (let row = 0; row < 4; row++) {
      let consecutiveCount = 1;
      let currentSymbol = grid[0][row];
      
      for (let reel = 1; reel < 5; reel++) {
        if (grid[reel][row] === currentSymbol || grid[reel][row] === 'WILD' || currentSymbol === 'WILD') {
          consecutiveCount++;
          
          // Break the sequence if it reaches 3
          if (consecutiveCount >= 3 && reel < 4) {
            const breakerSymbols = ['9', '10', 'J', 'Q'];
            let breaker;
            do {
              breaker = breakerSymbols[Math.floor(rng.next() * breakerSymbols.length)];
            } while (breaker === currentSymbol);
            
            grid[reel][row] = breaker;
            break;
          }
        } else {
          consecutiveCount = 1;
          currentSymbol = grid[reel][row];
        }
      }
    }
  }

  /**
   * 🎯 Ensure proper scatter distribution
   */
  private ensureScatterDistribution(grid: string[][], rng: any): void {
    const scatterPositions: Array<{reel: number, row: number}> = [];
    
    // Count existing scatters
    for (let reel = 0; reel < 5; reel++) {
      for (let row = 0; row < 4; row++) {
        if (grid[reel][row] === 'SCATTER') {
          scatterPositions.push({reel, row});
        }
      }
    }
    
    // Ensure at least 3 scatters when forcing
    while (scatterPositions.length < 3) {
      const reel = Math.floor(rng.next() * 5);
      const row = Math.floor(rng.next() * 4);
      
      // Don't place on already occupied scatter positions
      if (!scatterPositions.some(pos => pos.reel === reel && pos.row === row)) {
        grid[reel][row] = 'SCATTER';
        scatterPositions.push({reel, row});
      }
    }
    
    // Limit to maximum 5 scatters
    if (scatterPositions.length > 5) {
      const excessScatters = scatterPositions.slice(5);
      for (const pos of excessScatters) {
        grid[pos.reel][pos.row] = '9'; // Replace with low-value symbol
      }
    }
  }

  /**
   * 🧹 Clean up scatter cooldowns
   */
  public cleanupCooldowns(): void {
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    
    for (const [userId, lastScatter] of this.scatterCooldown.entries()) {
      if (lastScatter < oneHourAgo) {
        this.scatterCooldown.delete(userId);
      }
    }
  }
}

export const dynamicReelGenerator = new DynamicReelGenerator();
