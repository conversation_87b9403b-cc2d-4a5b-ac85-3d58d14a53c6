/**
 * AudioManager - Professional casino audio system
 * Manages sound effects, music, and audio feedback
 */

interface AudioTrack {
  id: string;
  audio: HTMLAudioElement;
  volume: number;
  loop: boolean;
}

export class AudioManager {
  private tracks: Map<string, AudioTrack>;
  private masterVolume: number;
  private soundEnabled: boolean;
  private musicEnabled: boolean;
  private currentMusic: string | null;

  constructor() {
    this.tracks = new Map();
    this.masterVolume = 0.7;
    this.soundEnabled = true;
    this.musicEnabled = true;
    this.currentMusic = null;
    
    this.initializeAudioTracks();
  }

  /**
   * Initialize all audio tracks
   */
  private initializeAudioTracks(): void {
    // Note: In a real implementation, you would load actual audio files
    // For now, we'll create placeholder audio elements that can be replaced
    // with real casino sound effects
    
    const audioTracks = [
      { id: 'spin', volume: 0.6, loop: false },
      { id: 'win-small', volume: 0.5, loop: false },
      { id: 'win-medium', volume: 0.7, loop: false },
      { id: 'win-big', volume: 0.8, loop: false },
      { id: 'win-jackpot', volume: 0.9, loop: false },
      { id: 'scatter', volume: 0.7, loop: false },
      { id: 'bonus', volume: 0.8, loop: false },
      { id: 'freespins', volume: 0.7, loop: false },
      { id: 'reel-stop', volume: 0.4, loop: false },
      { id: 'button-click', volume: 0.3, loop: false },
      { id: 'coin-drop', volume: 0.5, loop: false },
      { id: 'background-music', volume: 0.3, loop: true },
      { id: 'ambient-casino', volume: 0.2, loop: true },
    ];

    audioTracks.forEach(track => {
      // Create audio element (would load from actual file paths in production)
      const audio = new Audio();
      audio.preload = 'auto';
      audio.volume = track.volume * this.masterVolume;
      audio.loop = track.loop;
      
      // Add error handling
      audio.onerror = () => {
        console.warn(`Failed to load audio: ${track.id}`);
      };

      this.tracks.set(track.id, {
        id: track.id,
        audio,
        volume: track.volume,
        loop: track.loop,
      });
    });
  }

  /**
   * Play a sound effect
   */
  public playSound(trackId: string, volumeOverride?: number): void {
    if (!this.soundEnabled) return;
    
    const track = this.tracks.get(trackId);
    if (!track) {
      console.warn(`Audio track not found: ${trackId}`);
      return;
    }

    try {
      // Reset audio to beginning
      track.audio.currentTime = 0;
      
      // Set volume
      const volume = volumeOverride ?? track.volume;
      track.audio.volume = volume * this.masterVolume;
      
      // Play the sound
      const playPromise = track.audio.play();
      
      if (playPromise !== undefined) {
        playPromise.catch(error => {
          console.warn('Audio play failed:', error);
        });
      }
    } catch (error) {
      console.warn('Error playing sound:', error);
    }
  }

  /**
   * Play background music
   */
  public playMusic(trackId: string, fadeIn: boolean = true): void {
    if (!this.musicEnabled) return;
    
    // Stop current music
    if (this.currentMusic) {
      this.stopMusic(fadeIn);
    }
    
    const track = this.tracks.get(trackId);
    if (!track) {
      console.warn(`Music track not found: ${trackId}`);
      return;
    }

    try {
      track.audio.currentTime = 0;
      track.audio.volume = fadeIn ? 0 : track.volume * this.masterVolume;
      
      const playPromise = track.audio.play();
      
      if (playPromise !== undefined) {
        playPromise.then(() => {
          this.currentMusic = trackId;
          
          if (fadeIn) {
            this.fadeIn(track, 2000); // 2 second fade in
          }
        }).catch(error => {
          console.warn('Music play failed:', error);
        });
      }
    } catch (error) {
      console.warn('Error playing music:', error);
    }
  }

  /**
   * Stop background music
   */
  public stopMusic(fadeOut: boolean = true): void {
    if (!this.currentMusic) return;
    
    const track = this.tracks.get(this.currentMusic);
    if (!track) return;

    if (fadeOut) {
      this.fadeOut(track, 1000, () => {
        track.audio.pause();
        this.currentMusic = null;
      });
    } else {
      track.audio.pause();
      this.currentMusic = null;
    }
  }

  /**
   * Play spin sound effect
   */
  public playSpinSound(): void {
    this.playSound('spin');
  }

  /**
   * Play win sound based on win amount
   */
  public playWinSound(winAmount: number, betAmount: number): void {
    const multiplier = winAmount / betAmount;
    
    if (multiplier >= 50) {
      this.playSound('win-jackpot');
    } else if (multiplier >= 25) {
      this.playSound('win-big');
    } else if (multiplier >= 10) {
      this.playSound('win-medium');
    } else {
      this.playSound('win-small');
    }
  }

  /**
   * Play reel stop sound
   */
  public playReelStopSound(reelIndex: number, totalReels: number): void {
    // Slightly different pitch/timing for each reel
    const volumeVariation = 1 - (reelIndex * 0.1);
    this.playSound('reel-stop', volumeVariation);
  }

  /**
   * Play scatter sound
   */
  public playScatterSound(): void {
    this.playSound('scatter');
  }

  /**
   * Play bonus trigger sound
   */
  public playBonusSound(): void {
    this.playSound('bonus');
  }

  /**
   * Play free spins sound
   */
  public playFreeSpinsSound(): void {
    this.playSound('freespins');
  }

  /**
   * Play button click sound
   */
  public playButtonClick(): void {
    this.playSound('button-click');
  }

  /**
   * Play coin drop effect
   */
  public playCoinDrop(): void {
    this.playSound('coin-drop');
  }

  /**
   * Set master volume (0.0 to 1.0)
   */
  public setMasterVolume(volume: number): void {
    this.masterVolume = Math.max(0, Math.min(1, volume));
    
    // Update all track volumes
    this.tracks.forEach(track => {
      track.audio.volume = track.volume * this.masterVolume;
    });
  }

  /**
   * Enable/disable sound effects
   */
  public setSoundEnabled(enabled: boolean): void {
    this.soundEnabled = enabled;
  }

  /**
   * Enable/disable background music
   */
  public setMusicEnabled(enabled: boolean): void {
    this.musicEnabled = enabled;
    
    if (!enabled && this.currentMusic) {
      this.stopMusic(false);
    }
  }

  /**
   * Get current volume settings
   */
  public getVolumeSettings(): {
    master: number;
    soundEnabled: boolean;
    musicEnabled: boolean;
  } {
    return {
      master: this.masterVolume,
      soundEnabled: this.soundEnabled,
      musicEnabled: this.musicEnabled,
    };
  }

  /**
   * Fade in audio track
   */
  private fadeIn(track: AudioTrack, duration: number): void {
    const targetVolume = track.volume * this.masterVolume;
    const steps = 50;
    const stepTime = duration / steps;
    const volumeStep = targetVolume / steps;
    
    let currentStep = 0;
    
    const fadeInterval = setInterval(() => {
      currentStep++;
      track.audio.volume = volumeStep * currentStep;
      
      if (currentStep >= steps) {
        track.audio.volume = targetVolume;
        clearInterval(fadeInterval);
      }
    }, stepTime);
  }

  /**
   * Fade out audio track
   */
  private fadeOut(track: AudioTrack, duration: number, callback?: () => void): void {
    const initialVolume = track.audio.volume;
    const steps = 50;
    const stepTime = duration / steps;
    const volumeStep = initialVolume / steps;
    
    let currentStep = 0;
    
    const fadeInterval = setInterval(() => {
      currentStep++;
      track.audio.volume = initialVolume - (volumeStep * currentStep);
      
      if (currentStep >= steps || track.audio.volume <= 0) {
        track.audio.volume = 0;
        clearInterval(fadeInterval);
        
        if (callback) {
          callback();
        }
      }
    }, stepTime);
  }

  /**
   * Create audio sequence for special events
   */
  public playSequence(trackIds: string[], interval: number = 500): void {
    trackIds.forEach((trackId, index) => {
      setTimeout(() => {
        this.playSound(trackId);
      }, index * interval);
    });
  }

  /**
   * Play progressive win sound sequence
   */
  public playProgressiveWinSequence(winAmount: number, betAmount: number): void {
    const multiplier = winAmount / betAmount;
    
    if (multiplier >= 50) {
      // Epic win sequence
      this.playSequence(['scatter', 'bonus', 'win-jackpot'], 300);
    } else if (multiplier >= 25) {
      // Big win sequence
      this.playSequence(['scatter', 'win-big'], 400);
    } else if (multiplier >= 10) {
      // Medium win sequence
      this.playSequence(['win-medium'], 0);
    }
  }

  /**
   * Preload all audio tracks
   */
  public preloadAudio(): Promise<void[]> {
    const promises: Promise<void>[] = [];
    
    this.tracks.forEach(track => {
      const promise = new Promise<void>((resolve) => {
        if (track.audio.readyState >= 4) {
          resolve();
        } else {
          track.audio.addEventListener('canplaythrough', () => resolve(), { once: true });
          track.audio.addEventListener('error', () => resolve(), { once: true });
        }
      });
      
      promises.push(promise);
    });
    
    return Promise.all(promises);
  }

  /**
   * Cleanup audio resources
   */
  public dispose(): void {
    this.tracks.forEach(track => {
      track.audio.pause();
      track.audio.src = '';
      track.audio.load();
    });
    
    this.tracks.clear();
    this.currentMusic = null;
  }
}
