import { GameSession, User } from "@shared/schema";

interface PlayerMetrics {
  totalSpins: number;
  totalWagered: number;
  totalWon: number;
  currentStreak: number; // Positive for wins, negative for losses
  lastWinAmount: number;
  sessionDuration: number;
  avgBetSize: number;
  rtp: number; // Current session RTP
  ldwCount: number; // Loss Disguised as Win count
  nearMissCount: number;
  bonusCount: number;
  lastBonusSpins: number; // Spins since last bonus
}

interface PsychologyConfig {
  targetRTP: number; // 96%
  ldwThreshold: number; // 0.8 (80% of bet back = LDW)
  nearMissFrequency: number; // 15% of losing spins
  maxLossStreak: number; // Force win after this many losses
  bonusFrequency: number; // 1 in 100 spins average
  volatilityMode: 'low' | 'medium' | 'high';
}

export class SlotPsychologyEngine {
  private session: GameSession;
  private user: User;
  private bet: number;
  private metrics: PlayerMetrics;
  private config: PsychologyConfig;

  constructor(session: GameSession, user: User, bet: number) {
    this.session = session;
    this.user = user;
    this.bet = bet;
    this.config = {
      targetRTP: 0.96,
      ldwThreshold: 0.8,
      nearMissFrequency: 0.15,
      maxLossStreak: 8,
      bonusFrequency: 0.01,
      volatilityMode: 'medium'
    };
    this.calculateMetrics();
  }

  private calculateMetrics(): void {
    const totalBet = parseFloat(this.session.totalBet);
    const totalWon = parseFloat(this.session.totalWon);
    
    this.metrics = {
      totalSpins: this.session.spinsCount,
      totalWagered: totalBet,
      totalWon: totalWon,
      currentStreak: this.calculateCurrentStreak(),
      lastWinAmount: 0, // Would need to track this
      sessionDuration: Date.now() - this.session.createdAt.getTime(),
      avgBetSize: totalBet / Math.max(1, this.session.spinsCount),
      rtp: totalBet > 0 ? totalWon / totalBet : 0,
      ldwCount: 0, // Would need to track this
      nearMissCount: 0, // Would need to track this
      bonusCount: 0, // Would need to track this
      lastBonusSpins: this.session.spinsCount // Simplified
    };
  }

  private calculateCurrentStreak(): number {
    // Simplified - would need actual spin history
    return 0;
  }

  /**
   * Get adaptive symbol pool based on player psychology
   */
  getAdaptiveSymbolPool(): string[] {
    const basePool = this.getBaseSymbolPool();
    
    // 🎯 ADAPTIVE LOGIC BASED ON PLAYER STATE
    if (this.shouldIncreaseLowValueSymbols()) {
      return this.enhanceForSmallWins(basePool);
    }
    
    if (this.shouldIncreaseNearMisses()) {
      return this.enhanceForNearMisses(basePool);
    }
    
    if (this.shouldTriggerBonus()) {
      return this.enhanceForBonus(basePool);
    }
    
    return basePool;
  }

  private getBaseSymbolPool(): string[] {
    return [
      // High frequency symbols (lower value) - 60%
      '9', '9', '9', '9', '9', '9', '9', '9', '9', '9',
      '10', '10', '10', '10', '10', '10', '10', '10',
      'J', 'J', 'J', 'J', 'J', 'J', 'J',
      'Q', 'Q', 'Q', 'Q', 'Q', 'Q',
      // Medium frequency symbols - 30%
      'K', 'K', 'K', 'K', 'K',
      'A', 'A', 'A', 'A',
      // Low frequency symbols - 10%
      'WILD', 'WILD',
      'SCATTER'
    ];
  }

  private shouldIncreaseLowValueSymbols(): boolean {
    // Increase small wins if player is on losing streak
    return this.metrics.currentStreak < -3 || this.metrics.rtp < 0.85;
  }

  private shouldIncreaseNearMisses(): boolean {
    // More near misses when player might quit
    return this.metrics.currentStreak < -5 || this.metrics.sessionDuration > 1800000; // 30 min
  }

  private shouldTriggerBonus(): boolean {
    // Force bonus if it's been too long
    return this.metrics.lastBonusSpins > 150 || this.metrics.currentStreak < -this.config.maxLossStreak;
  }

  private enhanceForSmallWins(pool: string[]): string[] {
    // Add more low-value symbols for frequent small wins
    const enhanced = [...pool];
    for (let i = 0; i < 10; i++) {
      enhanced.push('9', '10', 'J');
    }
    return enhanced;
  }

  private enhanceForNearMisses(pool: string[]): string[] {
    // Slightly increase premium symbols for near-miss effect
    const enhanced = [...pool];
    enhanced.push('A', 'K', 'WILD');
    return enhanced;
  }

  private enhanceForBonus(pool: string[]): string[] {
    // Increase scatter symbols
    const enhanced = [...pool];
    enhanced.push('SCATTER', 'SCATTER', 'SCATTER');
    return enhanced;
  }

  /**
   * Apply psychological enhancements to the generated grid
   */
  enhanceGridForPsychology(grid: string[][], rng: any): string[][] {
    const enhanced = grid.map(reel => [...reel]);
    
    // 🎯 NEAR-MISS INJECTION
    if (this.shouldInjectNearMiss(rng)) {
      this.injectNearMiss(enhanced, rng);
    }
    
    // 🎯 BONUS SYMBOL PLACEMENT
    if (this.shouldPlaceBonusSymbols(rng)) {
      this.placeBonusSymbols(enhanced, rng);
    }
    
    return enhanced;
  }

  private shouldInjectNearMiss(rng: any): boolean {
    return rng.next() < this.config.nearMissFrequency;
  }

  private shouldPlaceBonusSymbols(rng: any): boolean {
    return this.shouldTriggerBonus() && rng.next() < 0.3;
  }

  private injectNearMiss(grid: string[][], rng: any): void {
    // Place 2 matching high-value symbols in visible positions
    const symbol = ['A', 'K', 'WILD'][Math.floor(rng.next() * 3)];
    const row = Math.floor(rng.next() * 4);
    
    grid[0][row] = symbol;
    grid[1][row] = symbol;
    // Third symbol is different - creates near miss
  }

  private placeBonusSymbols(grid: string[][], rng: any): void {
    // Place 2 scatter symbols to build anticipation
    const positions = [
      { reel: 0, row: Math.floor(rng.next() * 4) },
      { reel: 1, row: Math.floor(rng.next() * 4) }
    ];
    
    positions.forEach(pos => {
      grid[pos.reel][pos.row] = 'SCATTER';
    });
  }

  /**
   * Apply RTP smoothing and LDW mechanics
   */
  applyRTPSmoothing(totalWin: number, bet: number): {
    adjustedWin: number;
    isLDW: boolean;
    nearMiss: boolean;
  } {
    let adjustedWin = totalWin;
    let isLDW = false;
    let nearMiss = false;
    
    // 💰 RTP SMOOTHING
    if (this.metrics.rtp > this.config.targetRTP + 0.05) {
      // Player is winning too much, reduce wins
      adjustedWin = Math.max(0, totalWin * 0.8);
    } else if (this.metrics.rtp < this.config.targetRTP - 0.05) {
      // Player is losing too much, increase wins
      adjustedWin = totalWin * 1.2;
      
      // 🎯 FORCE SMALL WIN IF NO WIN
      if (totalWin === 0 && this.metrics.currentStreak < -5) {
        adjustedWin = bet * 0.6; // Small win to keep player engaged
        isLDW = true;
      }
    }
    
    // 🎭 LOSS DISGUISED AS WIN (LDW)
    if (totalWin > 0 && totalWin < bet * this.config.ldwThreshold) {
      isLDW = true;
    }
    
    return { adjustedWin, isLDW, nearMiss };
  }

  /**
   * Update player metrics after spin
   */
  async updatePlayerMetrics(totalWin: number, bet: number, isLDW: boolean, nearMiss: boolean): Promise<void> {
    // Update metrics (in a real implementation, this would update database)
    if (isLDW) this.metrics.ldwCount++;
    if (nearMiss) this.metrics.nearMissCount++;
    if (totalWin > bet * 2) this.metrics.bonusCount++;
  }

  /**
   * Get display data for client
   */
  getDisplayData(): any {
    return {
      rtp: this.metrics.rtp,
      streak: this.metrics.currentStreak,
      volatility: this.config.volatilityMode,
      lastBonusSpins: this.metrics.lastBonusSpins
    };
  }
}
