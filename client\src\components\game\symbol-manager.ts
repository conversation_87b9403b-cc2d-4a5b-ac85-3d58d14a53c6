/**
 * SymbolManager - <PERSON><PERSON> symbol definitions, weights, and payouts
 * Manages the symbol ecosystem for the slot machine
 */

export interface SymbolDefinition {
  id: string;
  name: string;
  weight: number;
  rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary' | 'scatter' | 'wild';
  payout: number[]; // [0, 0, 3-symbol, 4-symbol, 5-symbol]
  isWild: boolean;
  isScatter: boolean;
  color: string;
  emoji: string;
  multiplier?: number;
}

export class SymbolManager {
  private symbols: Map<string, SymbolDefinition>;
  private symbolPool: string[];
  private payoutTable: Map<string, number[]>;

  constructor() {
    this.symbols = new Map();
    this.symbolPool = [];
    this.payoutTable = new Map();
    this.initializeSymbols();
    this.createSymbolPool();
  }

  /**
   * Initialize all symbol definitions with balanced weights
   */
  private initializeSymbols(): void {
    const symbolDefinitions: SymbolDefinition[] = [
      // Common symbols (low payout, high frequency)
      {
        id: '9',
        name: '<PERSON>',
        weight: 35,
        rarity: 'common',
        payout: [0, 0, 2, 10, 50],
        isWild: false,
        isScatter: false,
        color: 'text-gray-300',
        emoji: '9',
      },
      {
        id: '10',
        name: 'Ten',
        weight: 30,
        rarity: 'common',
        payout: [0, 0, 3, 15, 75],
        isWild: false,
        isScatter: false,
        color: 'text-gray-300',
        emoji: '10',
      },
      
      // Uncommon symbols (medium payout, medium frequency)
      {
        id: 'J',
        name: 'Jack',
        weight: 25,
        rarity: 'uncommon',
        payout: [0, 0, 4, 20, 100],
        isWild: false,
        isScatter: false,
        color: 'text-blue-400',
        emoji: 'J',
      },
      {
        id: 'Q',
        name: 'Queen',
        weight: 20,
        rarity: 'uncommon',
        payout: [0, 0, 5, 25, 125],
        isWild: false,
        isScatter: false,
        color: 'text-purple-400',
        emoji: 'Q',
      },
      
      // Rare symbols (high payout, low frequency)
      {
        id: 'K',
        name: 'King',
        weight: 15,
        rarity: 'rare',
        payout: [0, 0, 6, 30, 150],
        isWild: false,
        isScatter: false,
        color: 'text-red-400',
        emoji: 'K',
      },
      {
        id: 'A',
        name: 'Ace',
        weight: 12,
        rarity: 'rare',
        payout: [0, 0, 8, 40, 200],
        isWild: false,
        isScatter: false,
        color: 'text-casino-gold',
        emoji: 'A',
      },
      
      // Special symbols
      {
        id: 'SCATTER',
        name: 'Scatter Star',
        weight: 3,
        rarity: 'scatter',
        payout: [0, 0, 2, 8, 75],
        isWild: false,
        isScatter: true,
        color: 'text-casino-gold',
        emoji: '⭐',
      },
      {
        id: 'WILD',
        name: 'Wild Joker',
        weight: 2,
        rarity: 'wild',
        payout: [0, 0, 20, 75, 300],
        isWild: true,
        isScatter: false,
        color: 'text-casino-red',
        emoji: '🃏',
        multiplier: 2,
      },
    ];

    // Initialize symbols map and payout table
    symbolDefinitions.forEach(symbol => {
      this.symbols.set(symbol.id, symbol);
      this.payoutTable.set(symbol.id, symbol.payout);
    });
  }

  /**
   * Create weighted symbol pool for random selection
   */
  private createSymbolPool(): void {
    this.symbolPool = [];
    
    this.symbols.forEach((symbol, id) => {
      for (let i = 0; i < symbol.weight; i++) {
        this.symbolPool.push(id);
      }
    });
  }

  /**
   * Get symbol by ID
   */
  public getSymbol(id: string): SymbolDefinition | undefined {
    return this.symbols.get(id);
  }

  /**
   * Get all symbols
   */
  public getAllSymbols(): SymbolDefinition[] {
    return Array.from(this.symbols.values());
  }

  /**
   * Get symbol pool for random selection
   */
  public getSymbolPool(): string[] {
    return [...this.symbolPool];
  }

  /**
   * Get symbol payout for specific count
   */
  public getSymbolPayout(symbolId: string, count: number): number {
    const payouts = this.payoutTable.get(symbolId);
    if (!payouts || count < 0 || count >= payouts.length) {
      return 0;
    }
    return payouts[count];
  }

  /**
   * Check if symbol is wild
   */
  public isWild(symbolId: string): boolean {
    const symbol = this.symbols.get(symbolId);
    return symbol?.isWild || false;
  }

  /**
   * Check if symbol is scatter
   */
  public isScatter(symbolId: string): boolean {
    const symbol = this.symbols.get(symbolId);
    return symbol?.isScatter || false;
  }

  /**
   * Check if two symbols match (including wild substitution)
   */
  public symbolsMatch(symbol1: string, symbol2: string): boolean {
    if (symbol1 === symbol2) return true;
    if (this.isWild(symbol1) || this.isWild(symbol2)) return true;
    return false;
  }

  /**
   * Get symbol multiplier
   */
  public getSymbolMultiplier(symbolId: string): number {
    const symbol = this.symbols.get(symbolId);
    return symbol?.multiplier || 1;
  }

  /**
   * Get symbols by rarity
   */
  public getSymbolsByRarity(rarity: string): SymbolDefinition[] {
    return Array.from(this.symbols.values()).filter(symbol => symbol.rarity === rarity);
  }

  /**
   * Calculate symbol frequency percentage
   */
  public getSymbolFrequency(symbolId: string): number {
    const symbol = this.symbols.get(symbolId);
    if (!symbol) return 0;
    
    const totalWeight = this.symbolPool.length;
    return (symbol.weight / totalWeight) * 100;
  }

  /**
   * Get display information for symbol
   */
  public getSymbolDisplay(symbolId: string): {
    emoji: string;
    color: string;
    name: string;
  } {
    const symbol = this.symbols.get(symbolId);
    
    if (!symbol) {
      return {
        emoji: '?',
        color: 'text-gray-500',
        name: 'Unknown',
      };
    }

    return {
      emoji: symbol.emoji,
      color: symbol.color,
      name: symbol.name,
    };
  }

  /**
   * Get complete payout table
   */
  public getPayoutTable(): { [symbolId: string]: number[] } {
    const table: { [symbolId: string]: number[] } = {};
    
    this.payoutTable.forEach((payouts, symbolId) => {
      table[symbolId] = [...payouts];
    });
    
    return table;
  }

  /**
   * Calculate theoretical RTP (Return to Player)
   */
  public calculateRTP(): number {
    let totalPayout = 0;
    let totalSpins = 0;
    
    // Simplified RTP calculation based on symbol frequencies and payouts
    this.symbols.forEach(symbol => {
      const frequency = this.getSymbolFrequency(symbol.id) / 100;
      
      for (let count = 3; count <= 5; count++) {
        const payout = this.getSymbolPayout(symbol.id, count);
        const probability = Math.pow(frequency, count);
        totalPayout += payout * probability;
        totalSpins += probability;
      }
    });
    
    return totalSpins > 0 ? (totalPayout / totalSpins) * 100 : 0;
  }

  /**
   * Adjust symbol weights for dynamic difficulty
   */
  public adjustSymbolWeights(adjustments: { [symbolId: string]: number }): void {
    Object.entries(adjustments).forEach(([symbolId, newWeight]) => {
      const symbol = this.symbols.get(symbolId);
      if (symbol && newWeight > 0) {
        symbol.weight = Math.max(1, Math.floor(newWeight));
      }
    });
    
    // Recreate symbol pool with new weights
    this.createSymbolPool();
  }

  /**
   * Get symbol statistics
   */
  public getSymbolStats(): {
    totalSymbols: number;
    totalWeight: number;
    rarityDistribution: { [rarity: string]: number };
    averagePayout: number;
  } {
    const totalSymbols = this.symbols.size;
    const totalWeight = this.symbolPool.length;
    const rarityDistribution: { [rarity: string]: number } = {};
    let totalPayout = 0;
    
    this.symbols.forEach(symbol => {
      rarityDistribution[symbol.rarity] = (rarityDistribution[symbol.rarity] || 0) + 1;
      totalPayout += symbol.payout.reduce((sum, payout) => sum + payout, 0);
    });
    
    return {
      totalSymbols,
      totalWeight,
      rarityDistribution,
      averagePayout: totalPayout / totalSymbols,
    };
  }
}

// Export singleton instance
export const symbolManager = new SymbolManager();
