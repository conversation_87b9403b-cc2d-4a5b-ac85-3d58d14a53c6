/**
 * 🎯 DEPOSIT-BASED BALANCE ENGINE
 * Controls win/loss based on user's deposit vs withdrawal ratio
 * Ensures users who withdraw too much get reduced luck, while depositors get better chances
 */

import { User } from "@shared/schema";
import { db } from "./db";
import { users, depositRequests, withdrawalRequests, spinResults } from "@shared/schema";
import { eq, desc, and, sum, count } from "drizzle-orm";

interface UserBalance {
  userId: number;
  totalDeposits: number;
  totalWithdrawals: number;
  netBalance: number; // deposits - withdrawals
  balanceRatio: number; // withdrawals / deposits
  recentSpins: number;
  recentWins: number;
  recentLosses: number;
  bigWinsCount: number;
  lastBigWin: Date | null;
  riskLevel: 'safe' | 'moderate' | 'high' | 'critical';
}

interface LuckAdjustment {
  shouldWin: boolean;
  winType: 'tiny' | 'small' | 'medium' | 'big' | 'jackpot';
  winChance: number; // 0-1
  maxWinMultiplier: number;
  scatterChance: number;
  reason: string;
  hookStrategy: 'normal' | 'frequent_small' | 'rare_medium' | 'very_rare_big';
}

export class DepositBalanceEngine {
  private userBalances = new Map<number, UserBalance>();
  
  // Balance thresholds
  private readonly SAFE_RATIO = 0.3;      // 30% withdrawal ratio = safe
  private readonly MODERATE_RATIO = 0.6;  // 60% withdrawal ratio = moderate risk
  private readonly HIGH_RATIO = 0.8;      // 80% withdrawal ratio = high risk
  private readonly CRITICAL_RATIO = 1.0;  // 100%+ withdrawal ratio = critical

  /**
   * 🎯 Main engine - calculates luck based on deposit/withdrawal balance
   */
  public async calculateLuckAdjustment(userId: number, betAmount: number): Promise<LuckAdjustment> {
    const userBalance = await this.getUserBalance(userId);
    
    console.log(`💰 User ${userId} Balance Analysis:`, {
      deposits: userBalance.totalDeposits,
      withdrawals: userBalance.totalWithdrawals,
      netBalance: userBalance.netBalance,
      ratio: userBalance.balanceRatio,
      riskLevel: userBalance.riskLevel
    });

    // Base luck adjustment based on balance ratio
    let adjustment = this.calculateBaseLuck(userBalance);
    
    // Apply recent activity modifiers
    adjustment = this.applyRecentActivityModifiers(adjustment, userBalance);
    
    // Apply bet amount considerations
    adjustment = this.applyBetAmountModifiers(adjustment, userBalance, betAmount);
    
    // Apply hook strategies for different user types
    adjustment = this.applyHookStrategy(adjustment, userBalance);

    console.log(`🎲 Luck Adjustment for User ${userId}:`, {
      shouldWin: adjustment.shouldWin,
      winChance: adjustment.winChance,
      strategy: adjustment.hookStrategy,
      reason: adjustment.reason
    });

    return adjustment;
  }

  /**
   * 💰 Calculate base luck based on deposit/withdrawal ratio
   */
  private calculateBaseLuck(userBalance: UserBalance): LuckAdjustment {
    const ratio = userBalance.balanceRatio;
    let baseAdjustment: LuckAdjustment;

    if (ratio <= this.SAFE_RATIO) {
      // User deposits much more than withdraws - REWARD THEM
      baseAdjustment = {
        shouldWin: Math.random() < 0.4, // 40% win chance
        winType: 'medium',
        winChance: 0.4,
        maxWinMultiplier: 5.0,
        scatterChance: 0.08, // 8% scatter chance
        reason: 'good_depositor_reward',
        hookStrategy: 'normal'
      };
    }
    else if (ratio <= this.MODERATE_RATIO) {
      // Moderate withdrawal ratio - NORMAL LUCK
      baseAdjustment = {
        shouldWin: Math.random() < 0.25, // 25% win chance
        winType: 'small',
        winChance: 0.25,
        maxWinMultiplier: 3.0,
        scatterChance: 0.05, // 5% scatter chance
        reason: 'moderate_balance',
        hookStrategy: 'normal'
      };
    }
    else if (ratio <= this.HIGH_RATIO) {
      // High withdrawal ratio - REDUCE LUCK, INCREASE SMALL WINS
      baseAdjustment = {
        shouldWin: Math.random() < 0.3, // 30% win chance but smaller wins
        winType: 'tiny',
        winChance: 0.3,
        maxWinMultiplier: 1.5,
        scatterChance: 0.03, // 3% scatter chance
        reason: 'high_withdrawal_hook',
        hookStrategy: 'frequent_small'
      };
    }
    else {
      // Critical ratio (withdraws more than deposits) - VERY REDUCED LUCK
      baseAdjustment = {
        shouldWin: Math.random() < 0.15, // 15% win chance
        winType: 'tiny',
        winChance: 0.15,
        maxWinMultiplier: 1.2,
        scatterChance: 0.02, // 2% scatter chance
        reason: 'critical_withdrawal_control',
        hookStrategy: 'rare_medium'
      };
    }

    return baseAdjustment;
  }

  /**
   * 📊 Apply recent activity modifiers
   */
  private applyRecentActivityModifiers(adjustment: LuckAdjustment, userBalance: UserBalance): LuckAdjustment {
    const winRate = userBalance.recentSpins > 0 ? userBalance.recentWins / userBalance.recentSpins : 0;

    // If user is winning too much recently, reduce their luck
    if (winRate > 0.4) {
      adjustment.winChance *= 0.7; // Reduce by 30%
      adjustment.maxWinMultiplier *= 0.8; // Reduce max win
      adjustment.scatterChance *= 0.6; // Reduce scatter chance
      adjustment.reason += '_recent_wins_control';
    }
    
    // If user is losing too much, give them a small boost (but not if they're critical)
    else if (winRate < 0.1 && userBalance.riskLevel !== 'critical') {
      adjustment.winChance *= 1.2; // Small boost
      adjustment.reason += '_loss_compensation';
    }

    return adjustment;
  }

  /**
   * 💸 Apply bet amount modifiers
   */
  private applyBetAmountModifiers(adjustment: LuckAdjustment, userBalance: UserBalance, betAmount: number): LuckAdjustment {
    // For high withdrawal users, encourage smaller bets by giving better odds on small bets
    if (userBalance.riskLevel === 'high' || userBalance.riskLevel === 'critical') {
      if (betAmount <= 5) {
        // Small bets get slightly better treatment to keep them playing
        adjustment.winChance *= 1.1;
        adjustment.reason += '_small_bet_hook';
      } else {
        // Large bets get reduced treatment
        adjustment.winChance *= 0.8;
        adjustment.maxWinMultiplier *= 0.7;
        adjustment.reason += '_large_bet_control';
      }
    }

    return adjustment;
  }

  /**
   * 🎣 Apply hook strategies based on user type
   */
  private applyHookStrategy(adjustment: LuckAdjustment, userBalance: UserBalance): LuckAdjustment {
    switch (userBalance.riskLevel) {
      case 'safe':
        // Good depositors - normal gameplay with occasional big wins
        if (Math.random() < 0.1) {
          adjustment.winType = 'big';
          adjustment.maxWinMultiplier = 8.0;
          adjustment.hookStrategy = 'normal';
        }
        break;

      case 'moderate':
        // Balanced users - standard gameplay
        adjustment.hookStrategy = 'normal';
        break;

      case 'high':
        // High withdrawal users - frequent small wins to keep them hooked
        adjustment.hookStrategy = 'frequent_small';
        if (adjustment.shouldWin) {
          adjustment.winType = 'tiny';
          adjustment.maxWinMultiplier = Math.min(adjustment.maxWinMultiplier, 2.0);
          
          // Give them frequent tiny wins instead of rare big ones
          if (Math.random() < 0.6) {
            adjustment.shouldWin = true;
            adjustment.winType = 'tiny';
            adjustment.maxWinMultiplier = 0.8 + Math.random() * 0.7; // 0.8x to 1.5x
          }
        }
        break;

      case 'critical':
        // Critical users - very rare medium wins to create hope
        adjustment.hookStrategy = 'very_rare_big';
        if (Math.random() < 0.05) { // 5% chance for a medium win
          adjustment.shouldWin = true;
          adjustment.winType = 'medium';
          adjustment.maxWinMultiplier = 3.0;
          adjustment.reason += '_rare_hope_win';
        } else {
          // Most of the time, tiny wins or losses
          adjustment.winType = 'tiny';
          adjustment.maxWinMultiplier = Math.min(adjustment.maxWinMultiplier, 1.3);
        }
        break;
    }

    return adjustment;
  }

  /**
   * 👤 Get or create user balance data
   */
  private async getUserBalance(userId: number): Promise<UserBalance> {
    if (this.userBalances.has(userId)) {
      return this.userBalances.get(userId)!;
    }

    const balance = await this.createUserBalance(userId);
    this.userBalances.set(userId, balance);
    return balance;
  }

  /**
   * 🔄 Create user balance from database
   */
  private async createUserBalance(userId: number): Promise<UserBalance> {
    // Get total deposits
    const deposits = await db
      .select({ total: sum(depositRequests.amount) })
      .from(depositRequests)
      .where(and(eq(depositRequests.userId, userId), eq(depositRequests.status, 'approved')));

    // Get total withdrawals
    const withdrawals = await db
      .select({ total: sum(withdrawalRequests.amount) })
      .from(withdrawalRequests)
      .where(and(eq(withdrawalRequests.userId, userId), eq(withdrawalRequests.status, 'completed')));

    // Get recent spin activity (last 50 spins)
    const recentSpins = await db
      .select()
      .from(spinResults)
      .where(eq(spinResults.userId, userId))
      .orderBy(desc(spinResults.createdAt))
      .limit(50);

    // Calculate metrics
    const totalDeposits = parseFloat(deposits[0]?.total || "0");
    const totalWithdrawals = parseFloat(withdrawals[0]?.total || "0");
    const netBalance = totalDeposits - totalWithdrawals;
    const balanceRatio = totalDeposits > 0 ? totalWithdrawals / totalDeposits : 0;

    // Analyze recent activity
    let recentWins = 0;
    let bigWinsCount = 0;
    let lastBigWin: Date | null = null;

    for (const spin of recentSpins) {
      const winAmount = parseFloat(spin.winAmount);
      const betAmount = parseFloat(spin.betAmount);
      
      if (winAmount > 0) {
        recentWins++;
        
        // Count big wins (5x+ bet)
        if (winAmount >= betAmount * 5) {
          bigWinsCount++;
          if (!lastBigWin) {
            lastBigWin = spin.createdAt;
          }
        }
      }
    }

    // Determine risk level
    let riskLevel: 'safe' | 'moderate' | 'high' | 'critical' = 'safe';
    if (balanceRatio >= this.CRITICAL_RATIO) {
      riskLevel = 'critical';
    } else if (balanceRatio >= this.HIGH_RATIO) {
      riskLevel = 'high';
    } else if (balanceRatio >= this.MODERATE_RATIO) {
      riskLevel = 'moderate';
    }

    return {
      userId,
      totalDeposits,
      totalWithdrawals,
      netBalance,
      balanceRatio,
      recentSpins: recentSpins.length,
      recentWins,
      recentLosses: recentSpins.length - recentWins,
      bigWinsCount,
      lastBigWin,
      riskLevel
    };
  }

  /**
   * 📝 Update user balance after spin
   */
  public updateAfterSpin(userId: number, won: boolean, winAmount: number, betAmount: number): void {
    const balance = this.userBalances.get(userId);
    if (!balance) return;

    // Update recent activity
    balance.recentSpins++;
    if (won) {
      balance.recentWins++;
      
      // Track big wins
      if (winAmount >= betAmount * 5) {
        balance.bigWinsCount++;
        balance.lastBigWin = new Date();
      }
    } else {
      balance.recentLosses++;
    }

    // Keep recent activity window manageable
    if (balance.recentSpins > 50) {
      balance.recentSpins = 50;
      balance.recentWins = Math.floor(balance.recentWins * 0.9);
      balance.recentLosses = balance.recentSpins - balance.recentWins;
    }
  }

  /**
   * 🔄 Refresh user balance data (call when deposits/withdrawals change)
   */
  public async refreshUserBalance(userId: number): Promise<void> {
    this.userBalances.delete(userId);
    await this.getUserBalance(userId);
  }

  /**
   * 📊 Get user balance summary for admin
   */
  public async getUserBalanceSummary(userId: number): Promise<any> {
    const balance = await this.getUserBalance(userId);
    
    return {
      userId: balance.userId,
      totalDeposits: balance.totalDeposits,
      totalWithdrawals: balance.totalWithdrawals,
      netBalance: balance.netBalance,
      balanceRatio: balance.balanceRatio,
      riskLevel: balance.riskLevel,
      recentWinRate: balance.recentSpins > 0 ? balance.recentWins / balance.recentSpins : 0,
      bigWinsCount: balance.bigWinsCount,
      lastBigWin: balance.lastBigWin
    };
  }

  /**
   * 🧹 Clean up old balance data
   */
  public cleanupBalances(): void {
    // Keep only active users (those with recent activity)
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    
    for (const [userId, balance] of this.userBalances.entries()) {
      if (balance.lastBigWin && balance.lastBigWin.getTime() < oneHourAgo && balance.recentSpins === 0) {
        this.userBalances.delete(userId);
      }
    }
  }
}

export const depositBalanceEngine = new DepositBalanceEngine();
