@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Inter:wght@300;400;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Casino Color Palette */
  --casino-gold: 51 100% 50%; /* #FFD700 */
  --casino-purple: 271 76% 53%; /* #8A2BE2 */
  --casino-red: 348 83% 47%; /* #DC143C */
  --neon-cyan: 180 100% 50%; /* #00FFFF */
  --neon-pink: 328 100% 54%; /* #FF1493 */
  --deep-navy: 240 100% 3%; /* #0a0a0f */
  --card-bg: 240 29% 14%; /* rgba(26, 26, 46, 0.85) */
  
  /* Shadcn Color System */
  --background: 240 100% 3%; /* Deep navy for casino feel */
  --foreground: 0 0% 98%; /* Near white text */
  --muted: 240 29% 14%; /* Card background */
  --muted-foreground: 240 5% 65%; /* Muted text */
  --popover: 240 29% 14%; /* Card background */
  --popover-foreground: 0 0% 98%; /* White text */
  --card: 240 29% 14%; /* Card background */
  --card-foreground: 0 0% 98%; /* White text */
  --border: 51 100% 50%; /* Casino gold borders */
  --input: 240 29% 14%; /* Input background */
  --primary: 51 100% 50%; /* Casino gold primary */
  --primary-foreground: 240 100% 3%; /* Dark text on gold */
  --secondary: 271 76% 53%; /* Casino purple */
  --secondary-foreground: 0 0% 98%; /* White text */
  --accent: 348 83% 47%; /* Casino red */
  --accent-foreground: 0 0% 98%; /* White text */
  --destructive: 0 84% 60%; /* Red for errors */
  --destructive-foreground: 0 0% 98%; /* White text */
  --ring: 51 100% 50%; /* Casino gold focus ring */
  --radius: 0.75rem; /* Larger radius for casino feel */
}

.dark {
  --background: 240 100% 3%;
  --foreground: 0 0% 98%;
  --muted: 240 29% 14%;
  --muted-foreground: 240 5% 65%;
  --popover: 240 29% 14%;
  --popover-foreground: 0 0% 98%;
  --card: 240 29% 14%;
  --card-foreground: 0 0% 98%;
  --border: 51 100% 50%;
  --input: 240 29% 14%;
  --primary: 51 100% 50%;
  --primary-foreground: 240 100% 3%;
  --secondary: 271 76% 53%;
  --secondary-foreground: 0 0% 98%;
  --accent: 348 83% 47%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 0 0% 98%;
  --ring: 51 100% 50%;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter antialiased;
  }

  /* Casino-specific typography */
  .font-orbitron {
    font-family: 'Orbitron', monospace;
  }

  .font-inter {
    font-family: 'Inter', sans-serif;
  }
}

@layer components {
  /* Glass Morphism Effects */
  .glass-card {
    background: linear-gradient(145deg, 
      hsla(var(--card)) 0%, 
      hsla(var(--secondary) / 0.1) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid hsla(var(--border) / 0.3);
    box-shadow: 
      0 8px 32px hsla(0 0% 0% / 0.3),
      inset 0 1px 0 hsla(0 0% 100% / 0.1);
  }

  /* Casino Color Utilities */
  .text-casino-gold {
    color: hsl(var(--casino-gold));
  }

  .text-casino-purple {
    color: hsl(var(--casino-purple));
  }

  .text-casino-red {
    color: hsl(var(--casino-red));
  }

  .text-neon-cyan {
    color: hsl(var(--neon-cyan));
  }

  .text-neon-pink {
    color: hsl(var(--neon-pink));
  }

  .bg-casino-gold {
    background-color: hsl(var(--casino-gold));
  }

  .bg-casino-purple {
    background-color: hsl(var(--casino-purple));
  }

  .bg-casino-red {
    background-color: hsl(var(--casino-red));
  }

  .bg-neon-cyan {
    background-color: hsl(var(--neon-cyan));
  }

  .bg-neon-pink {
    background-color: hsl(var(--neon-pink));
  }

  .bg-deep-navy {
    background-color: hsl(var(--deep-navy));
  }

  .border-casino-gold {
    border-color: hsl(var(--casino-gold));
  }

  .border-casino-purple {
    border-color: hsl(var(--casino-purple));
  }

  .border-casino-red {
    border-color: hsl(var(--casino-red));
  }

  /* Casino-specific gradients */
  .bg-casino-gradient {
    background: linear-gradient(135deg, 
      hsl(var(--deep-navy)) 0%, 
      hsl(240 29% 14%) 50%, 
      hsl(var(--casino-purple)) 100%);
  }

  .bg-gold-gradient {
    background: linear-gradient(45deg, 
      hsl(var(--casino-gold)), 
      hsl(39 100% 50%), 
      hsl(var(--casino-gold)));
  }

  .bg-neon-gradient {
    background: linear-gradient(45deg, 
      hsl(var(--casino-purple)), 
      hsl(var(--neon-pink)), 
      hsl(var(--neon-cyan)));
  }
}

@layer utilities {
  /* Slot Machine Animations */
  @keyframes spinReel {
    0% { 
      transform: translateY(-100%) rotateX(0deg); 
      opacity: 0; 
    }
    50% { 
      transform: translateY(-50%) rotateX(180deg); 
      opacity: 0.5; 
    }
    100% { 
      transform: translateY(0) rotateX(360deg); 
      opacity: 1; 
    }
  }

  @keyframes glowPulse {
    0% { 
      box-shadow: 0 0 20px hsla(var(--casino-gold) / 0.5), 
                  inset 0 0 20px hsla(var(--casino-gold) / 0.1); 
    }
    100% { 
      box-shadow: 0 0 40px hsla(var(--casino-gold) / 0.8), 
                  inset 0 0 30px hsla(var(--casino-gold) / 0.2); 
    }
  }

  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }

  @keyframes winCelebration {
    0% { 
      transform: translate(-50%, -50%) scale(0.5) rotate(-10deg); 
      opacity: 0; 
    }
    20% { 
      transform: translate(-50%, -50%) scale(1.2) rotate(5deg); 
      opacity: 1; 
    }
    100% { 
      transform: translate(-50%, -50%) scale(1) rotate(0deg); 
      opacity: 1; 
    }
  }

  @keyframes cascadeDrop {
    0% { 
      transform: translateY(-100px); 
      opacity: 0; 
    }
    100% { 
      transform: translateY(0); 
      opacity: 1; 
    }
  }

  @keyframes symbolHighlight {
    0%, 100% { 
      transform: scale(1); 
      filter: brightness(1); 
    }
    50% { 
      transform: scale(1.1); 
      filter: brightness(1.5) drop-shadow(0 0 20px hsl(var(--casino-gold))); 
    }
  }

  @keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  @keyframes messageSlideIn {
    0% {
      transform: translate(-50%, -50%) scale(0.8);
      opacity: 0;
    }
    100% {
      transform: translate(-50%, -50%) scale(1);
      opacity: 1;
    }
  }

  @keyframes messageSlideOut {
    0% {
      transform: translate(-50%, -50%) scale(1);
      opacity: 1;
    }
    100% {
      transform: translate(-50%, -50%) scale(0.8);
      opacity: 0;
    }
  }

  /* Animation Classes */
  .animate-spin-reel {
    animation: spinReel 2s ease-out;
  }

  .animate-glow-pulse {
    animation: glowPulse 2s ease-in-out infinite alternate;
  }

  .animate-shimmer {
    background: linear-gradient(90deg, transparent, hsla(var(--casino-gold) / 0.4), transparent);
    background-size: 200% 100%;
    animation: shimmer 3s ease-in-out infinite;
  }

  .animate-win-celebration {
    animation: winCelebration 4s ease-out;
  }

  .animate-cascade-drop {
    animation: cascadeDrop 0.8s ease-out;
  }

  .animate-symbol-highlight {
    animation: symbolHighlight 1s ease-in-out;
  }

  .animate-gradient-shift {
    background-size: 200% 200%;
    animation: gradientShift 4s ease-in-out infinite;
  }

  /* Slot Machine Specific Styles */
  .symbol-container {
    background: linear-gradient(145deg, hsl(240 29% 14%), hsl(240 20% 20%));
    border: 2px solid hsla(var(--casino-gold) / 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .symbol-container:hover {
    border-color: hsl(var(--casino-gold));
    box-shadow: 0 0 20px hsla(var(--casino-gold) / 0.5);
  }

  .symbol-container.winning-symbol {
    animation: symbolHighlight 1s ease-in-out;
    border-color: hsl(var(--casino-gold));
    box-shadow: 0 0 30px hsla(var(--casino-gold) / 0.8);
  }

  .reel-spinning .symbol-container {
    animation: spinReel 0.1s linear infinite;
  }

  .shimmer-effect {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, hsla(var(--casino-gold) / 0.4), transparent);
    transition: left 0.5s ease;
  }

  .symbol-container:hover .shimmer-effect {
    left: 100%;
  }

  /* Win Line Effects */
  .win-line {
    background: linear-gradient(90deg, transparent, hsla(var(--casino-gold) / 0.6), transparent);
    animation: shimmer 1s infinite;
  }

  /* Progressive Elements */
  .progressive-bar {
    background: linear-gradient(90deg, 
      hsl(var(--casino-gold)), 
      hsl(var(--neon-pink)), 
      hsl(var(--neon-cyan)));
    background-size: 200% 100%;
    animation: shimmer 3s ease-in-out infinite;
  }

  /* Balance Animation States */
  .balance-increasing {
    animation: glowPulse 0.5s ease-in-out;
    color: hsl(120 100% 50%) !important; /* Green */
  }

  .balance-decreasing {
    color: hsl(0 100% 50%) !important; /* Red */
  }

  /* Value Change Animation */
  .value-changing {
    transform: scale(1.1);
    transition: all 0.3s ease;
  }

  /* Button Hover Effects */
  .btn-hover-effect {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .btn-hover-effect::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, hsla(0 0% 100% / 0.3), transparent);
    transition: left 0.5s ease;
  }

  .btn-hover-effect:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px hsla(0 0% 0% / 0.3);
  }

  .btn-hover-effect:hover::before {
    left: 100%;
  }

  .btn-hover-effect:active {
    transform: translateY(0) scale(0.95);
  }

  /* Mobile Optimizations */
  @media (max-width: 768px) {
    .reel-grid {
      transform: scale(0.8);
    }
    
    .control-panel {
      flex-direction: column;
      gap: 1rem;
    }

    .glass-card {
      backdrop-filter: blur(10px);
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .glass-card {
      background: hsl(var(--card));
      border: 2px solid hsl(var(--border));
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .animate-spin-reel,
    .animate-glow-pulse,
    .animate-shimmer,
    .animate-win-celebration,
    .animate-cascade-drop,
    .animate-symbol-highlight,
    .animate-gradient-shift {
      animation: none;
    }

    .btn-hover-effect {
      transition: none;
    }
  }

  /* Focus styles for accessibility */
  .focus-visible:focus {
    outline: 2px solid hsl(var(--casino-gold));
    outline-offset: 2px;
  }

  /* Custom scrollbar for webkit browsers */
  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: hsla(var(--muted) / 0.3);
    border-radius: 4px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: hsla(var(--casino-gold) / 0.6);
    border-radius: 4px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--casino-gold));
  }
}

/* Additional casino-specific utilities */
.neon-border {
  border: 2px solid transparent;
  background: linear-gradient(145deg, var(--tw-gradient-stops)) border-box;
  border-image: linear-gradient(45deg, 
    hsl(var(--casino-gold)), 
    hsl(var(--neon-pink)), 
    hsl(var(--neon-cyan)), 
    hsl(var(--casino-gold))) 1;
}

.text-glow {
  text-shadow: 
    0 0 10px currentColor,
    0 0 20px currentColor,
    0 0 30px currentColor;
}

.casino-shadow {
  box-shadow: 
    0 0 30px hsla(var(--casino-gold) / 0.4),
    inset 0 1px 0 hsla(0 0% 100% / 0.1);
}

/* Ensure proper layering for modals and overlays */
.modal-overlay {
  z-index: 1000;
}

.win-celebration-overlay {
  z-index: 1500;
}

.settings-modal-overlay {
  z-index: 2000;
}
