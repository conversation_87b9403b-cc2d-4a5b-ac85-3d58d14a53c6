import { Howl, Howler } from 'howler';

/**
 * AudioManager.js - Handles all game audio with premium quality using Howler.js
 */
class AudioManager {
    constructor(scene) {
        this.scene = scene;
        this.sounds = {};
        this.isMuted = false;
        this._volume = 0.7;
        Howler.volume(this._volume);
        this.isLoaded = false;
        this.currentlyPlayingSoundIds = new Map();
        
        // Load all sound effects
        this.loadSounds();
    }

    /**
     * Load all game sounds using Howler.js
     */
    loadSounds() {
        const soundFiles = {
            // Background music
            'bgm': 'assets/audio/bgm.mp3',
            
            // UI Sounds
            'button_click': 'assets/audio/button_click.mp3',
            'spin_start': 'assets/audio/spin_start.mp3',
            'reel_spin': 'assets/audio/reel_spin.mp3',
            'reel_stop': 'assets/audio/reel_stop.mp3',
            
            // Win Sounds
            'win_small': 'assets/audio/win_small.mp3',
            'win_medium': 'assets/audio/win_medium.mp3',
            'win_big': 'assets/audio/win_big.mp3',
            'win_mega': 'assets/audio/win_mega.mp3',
            'coin_collect': 'assets/audio/coin_collect.mp3',
            
            // Special Features
            'free_spins_trigger': 'assets/audio/free_spins_trigger.mp3',
            'free_spins_win': 'assets/audio/free_spins_win.mp3',
            'wild_transform': 'assets/audio/wild_transform.mp3',
            'scatter_land': 'assets/audio/scatter_land.mp3',
            
            // Ambient Sounds
            'ambient_casino': 'assets/audio/ambient_casino.mp3'
        };

        let soundsToLoad = Object.keys(soundFiles).length;
        let soundsLoaded = 0;

        Object.entries(soundFiles).forEach(([key, path]) => {
            const sound = new Howl({
                src: [path],
                volume: (key === 'bgm' || key === 'ambient_casino') ? 0.5 : 1.0,
                loop: key === 'bgm' || key === 'ambient_casino' || key === 'reel_spin',
                preload: true,
                rate: key === 'reel_spin' ? 1.2 : 1.0,
                onload: () => {
                    console.log(`Sound loaded: ${key}`);
                    soundsLoaded++;
                    if (soundsLoaded === soundsToLoad) {
                        this.isLoaded = true;
                        console.log('All sounds loaded.');
                    }
                },
                onloaderror: (id, err) => {
                    console.error(`Failed to load sound: ${key}`, err);
                    this.createFallbackSound(key);
                    soundsLoaded++;
                    if (soundsLoaded === soundsToLoad) {
                        this.isLoaded = true;
                        console.warn('All sounds attempted to load, some failed.');
                    }
                },
                onplayerror: (id, err) => {
                    console.error(`Error playing sound ${key}:`, err);
                },
                onend: () => {
                    if (this.currentlyPlayingSoundIds.has(key)) {
                        if (!this.sounds[key].loop()) {
                            this.currentlyPlayingSoundIds.delete(key);
                        }
                    }
                }
            });
            this.sounds[key] = sound;
        });
    }

    /**
     * Create a fallback sound object for sounds that fail to load
     * @param {string} key - Sound key
     */
    createFallbackSound(key) {
        this.sounds[key] = {
            play: () => { console.warn(`Fallback: Play called on missing sound ${key}`); return null; },
            stop: () => { console.warn(`Fallback: Stop called on missing sound ${key}`); },
            volume: () => {},
            rate: () => {},
            stereo: () => {},
            fade: () => {},
            loop: () => false,
            playing: () => false,
        };
    }

    /**
     * Play a sound effect
     * @param {string} soundKey - Key of the sound to play
     * @param {Object} options - Additional options { volume, rate, delay, x, y }
     * @returns {number|null} Sound ID from Howler or null if not played
     */
    play(soundKey, options = {}) {
        if (this.isMuted || !this.isLoaded) return null;
        
        const sound = this.sounds[soundKey];
        if (!sound || typeof sound.play !== 'function') {
            console.warn(`Sound not found or invalid: ${soundKey}`);
            return null;
        }

        if (sound.playing() && !sound.loop() && soundKey !== 'reel_spin') {
            sound.stop();
        }
        
        const playSound = () => {
            const soundId = sound.play();
            if (soundId === null && sound.state() === 'loading') {
                console.warn(`Sound ${soundKey} is still loading. Playback might be delayed or skipped.`);
                sound.once('load', () => {
                    const newId = sound.play();
                    this.sounds[soundKey].volume(options.volume !== undefined ? options.volume : 1.0);
                    if (options.rate) this.sounds[soundKey].rate(options.rate, newId);
                    if (options.x !== undefined && options.y !== undefined && this.scene && this.scene.cameras.main.width > 0) {
                        const pan = (options.x / this.scene.cameras.main.width) * 2 - 1;
                        this.sounds[soundKey].stereo(Math.max(-1, Math.min(1, pan)), newId);
                    }
                    this.currentlyPlayingSoundIds.set(soundKey, newId);
                });
                return;
            }

            this.sounds[soundKey].volume(options.volume !== undefined ? options.volume : 1.0, soundId);
            if (options.rate) this.sounds[soundKey].rate(options.rate, soundId);
            
            if (options.x !== undefined && options.y !== undefined && this.scene && this.scene.cameras.main.width > 0) {
                const pan = (options.x / this.scene.cameras.main.width) * 2 - 1;
                this.sounds[soundKey].stereo(Math.max(-1, Math.min(1, pan)), soundId);
            } else {
                this.sounds[soundKey].stereo(0, soundId);
            }
            
            this.currentlyPlayingSoundIds.set(soundKey, soundId);
            return soundId;
        };

        if (options.delay) {
            setTimeout(playSound, options.delay);
            return null;
        } else {
            return playSound();
        }
    }

    /**
     * Play background music with fade in
     */
    playBGM() {
        if (this.isMuted || !this.isLoaded) return;
        
        const bgm = this.sounds.bgm;
        const ambient = this.sounds.ambient_casino;

        if (bgm && typeof bgm.play === 'function' && !bgm.playing()) {
            bgm.volume(0);
            bgm.play();
            bgm.fade(0, this._volume * 0.7, 2000);
        }
        if (ambient && typeof ambient.play === 'function' && !ambient.playing()) {
            ambient.volume(0);
            ambient.play();
            ambient.fade(0, this._volume * 0.3, 2000);
        }
    }

    /**
     * Stop background music with fade out
     */
    stopBGM() {
        if (!this.isLoaded) return;
        
        const bgm = this.sounds.bgm;
        const ambient = this.sounds.ambient_casino;

        if (bgm && typeof bgm.fade === 'function' && bgm.playing()) {
            bgm.fade(bgm.volume(), 0, 1000);
            bgm.once('fade', () => { bgm.stop(); });
        }
        if (ambient && typeof ambient.fade === 'function' && ambient.playing()) {
            ambient.fade(ambient.volume(), 0, 1000);
            ambient.once('fade', () => { ambient.stop(); });
        }
    }

    /**
     * Play win celebration sound sequence
     * @param {number} winAmount - Amount won
     * @param {number} betAmount - Current bet amount
     */
    playWinSound(winAmount, betAmount) {
        if (!this.isLoaded || this.isMuted) return;
        
        const multiplier = betAmount > 0 ? winAmount / betAmount : winAmount > 0 ? 100 : 0;
        
        ['win_small', 'win_medium', 'win_big', 'win_mega', 'coin_collect'].forEach(key => {
            const sound = this.sounds[key];
            if (sound && typeof sound.stop === 'function' && sound.playing()) {
                sound.stop();
            }
        });

        let mainWinSoundKey;
        let coinCollectDelay = 400;
        let coinCollectVolume = 0.5;
        let coinCollectRate = 1.1;

        if (multiplier >= 50) {
            mainWinSoundKey = 'win_mega';
            coinCollectDelay = 1000; coinCollectVolume = 0.8; coinCollectRate = 0.8;
        } else if (multiplier >= 25) {
            mainWinSoundKey = 'win_big';
            coinCollectDelay = 800; coinCollectVolume = 0.7; coinCollectRate = 0.9;
        } else if (multiplier >= 10) {
            mainWinSoundKey = 'win_medium';
            coinCollectDelay = 600; coinCollectVolume = 0.6; coinCollectRate = 1.0;
        } else if (winAmount > 0) {
            mainWinSoundKey = 'win_small';
        } else {
            return;
        }

        this.play(mainWinSoundKey, { volume: mainWinSoundKey === 'win_mega' ? 1.0 : (mainWinSoundKey === 'win_big' ? 0.9 : (mainWinSoundKey === 'win_medium' ? 0.8 : 0.7)) });
        
        if (winAmount > 0) {
            this.play('coin_collect', { volume: coinCollectVolume, rate: coinCollectRate, delay: coinCollectDelay });
        }
    }

    /**
     * Play reel spin sound. This sound is looping and needs specific management.
     * @param {number} reel - Reel index (for potential rate/pan adjustments per reel)
     */
    playReelSpin(reel) {
        if (this.isMuted || !this.isLoaded) return;
        const sound = this.sounds.reel_spin;
        if (sound && typeof sound.play === 'function') {
            if (!sound.playing()) {
                const soundId = sound.play();
                sound.rate(1.0 + (reel * 0.05), soundId);
                if (this.scene && this.scene.symbolSprites && this.scene.symbolSprites[reel] && this.scene.symbolSprites[reel][0]) {
                    const pan = (this.scene.symbolSprites[reel][0].x / this.scene.cameras.main.width) * 2 - 1;
                    sound.stereo(Math.max(-1, Math.min(1, pan)), soundId);
                } else {
                    sound.stereo(0, soundId);
                }
                this.currentlyPlayingSoundIds.set('reel_spin_master', soundId);
            }
        }
    }

    /**
     * Stop reel spin sound
     */
    stopReelSpin() {
        if (!this.isLoaded) return;
        const sound = this.sounds.reel_spin;
        if (sound && typeof sound.stop === 'function' && sound.playing()) {
            const soundId = this.currentlyPlayingSoundIds.get('reel_spin_master');
            if (soundId) {
                sound.stop(soundId);
            } else {
                sound.stop();
            }
            this.currentlyPlayingSoundIds.delete('reel_spin_master');
        }
    }
    
    /**
     * Stop a specific sound by key
     * @param {string} soundKey 
     */
    stop(soundKey) {
        if (!this.isLoaded) return;
        const sound = this.sounds[soundKey];
        if (sound && typeof sound.stop === 'function') {
            const soundId = this.currentlyPlayingSoundIds.get(soundKey);
            if (soundId) {
                sound.stop(soundId);
                this.currentlyPlayingSoundIds.delete(soundKey);
            } else if (sound.playing()) {
                sound.stop();
            }
        }
    }

    /**
     * Toggle mute state for all sounds
     */
    toggleMute() {
        this.isMuted = !this.isMuted;
        Howler.mute(this.isMuted);
        
        if (!this.isMuted) {
            this.playBGM();
        } else {
            const bgm = this.sounds.bgm;
            const ambient = this.sounds.ambient_casino;
            if (bgm && bgm.playing()) bgm.pause();
            if (ambient && ambient.playing()) ambient.pause();
        }
        return this.isMuted;
    }
    
    /**
     * Set master volume for all sounds
     * @param {number} volumeLevel - Volume level (0-1)
     */
    setVolume(volumeLevel) {
        this._volume = Math.max(0, Math.min(1, volumeLevel));
        Howler.volume(this._volume);
    }

    /**
     * Get current master volume
     * @returns {number} Volume level (0-1)
     */
    getVolume() {
        return this._volume;
    }
} 