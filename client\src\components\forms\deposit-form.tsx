import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DollarSign, CreditCard, AlertCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { z } from "zod";

const depositSchema = z.object({
  amount: z.number().min(300, "Minimum deposit is 300 BDT"),
  bkashNumber: z.string().regex(/^[0-9]{11}$/, "Bkash number must be 11 digits"),
  transactionId: z.string().min(5, "Transaction ID must be at least 5 characters"),
});

interface DepositFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function DepositForm({ onSuccess, onCancel }: DepositFormProps) {
  const [formData, setFormData] = useState({
    amount: "",
    bkashNumber: "",
    transactionId: "",
  });
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setLoading(true);
      
      // Validate form data
      const validatedData = depositSchema.parse({
        amount: parseFloat(formData.amount),
        bkashNumber: formData.bkashNumber,
        transactionId: formData.transactionId,
      });

      // Submit deposit request
      const response = await fetch('/api/deposits', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(validatedData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to submit deposit request');
      }

      toast({
        title: "Deposit Request Submitted",
        description: "Your deposit request has been submitted and is pending admin approval.",
      });

      // Reset form
      setFormData({
        amount: "",
        bkashNumber: "",
        transactionId: "",
      });

      onSuccess?.();
    } catch (error) {
      console.error('Deposit error:', error);
      
      if (error instanceof z.ZodError) {
        toast({
          title: "Validation Error",
          description: error.errors[0].message,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Deposit Failed",
          description: error instanceof Error ? error.message : "Failed to submit deposit request",
          variant: "destructive",
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Card className="glass-card border-casino-gold/30 max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="text-casino-gold flex items-center">
          <DollarSign className="w-5 h-5 mr-2" />
          Deposit Funds
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Instructions */}
          <div className="bg-casino-gold/10 border border-casino-gold/30 rounded-lg p-4">
            <div className="flex items-start space-x-2">
              <AlertCircle className="w-5 h-5 text-casino-gold mt-0.5" />
              <div className="text-sm">
                <p className="text-casino-gold font-medium mb-2">Deposit Instructions:</p>
                <ol className="text-gray-300 space-y-1 text-xs">
                  <li>1. Send money to our Bkash number via Bkash app</li>
                  <li>2. Copy the transaction ID from Bkash</li>
                  <li>3. Fill out this form with your details</li>
                  <li>4. Wait for admin approval (usually within 30 minutes)</li>
                </ol>
              </div>
            </div>
          </div>

          {/* Amount */}
          <div className="space-y-2">
            <Label htmlFor="amount" className="text-gray-300">
              Amount (BDT) *
            </Label>
            <Input
              id="amount"
              type="number"
              placeholder="Minimum 300 BDT"
              value={formData.amount}
              onChange={(e) => handleInputChange('amount', e.target.value)}
              className="bg-black/20 border-gray-600 text-white placeholder-gray-400"
              min="300"
              step="1"
              required
            />
            <p className="text-xs text-gray-400">Minimum deposit: 300 BDT</p>
          </div>

          {/* Bkash Number */}
          <div className="space-y-2">
            <Label htmlFor="bkashNumber" className="text-gray-300">
              Your Bkash Number *
            </Label>
            <Input
              id="bkashNumber"
              type="tel"
              placeholder="01XXXXXXXXX"
              value={formData.bkashNumber}
              onChange={(e) => handleInputChange('bkashNumber', e.target.value.replace(/\D/g, ''))}
              className="bg-black/20 border-gray-600 text-white placeholder-gray-400"
              maxLength={11}
              required
            />
            <p className="text-xs text-gray-400">Enter your 11-digit Bkash number</p>
          </div>

          {/* Transaction ID */}
          <div className="space-y-2">
            <Label htmlFor="transactionId" className="text-gray-300">
              Bkash Transaction ID *
            </Label>
            <Input
              id="transactionId"
              type="text"
              placeholder="Enter transaction ID from Bkash"
              value={formData.transactionId}
              onChange={(e) => handleInputChange('transactionId', e.target.value)}
              className="bg-black/20 border-gray-600 text-white placeholder-gray-400"
              required
            />
            <p className="text-xs text-gray-400">Copy this from your Bkash transaction receipt</p>
          </div>

          {/* Bkash Info */}
          <div className="bg-green-500/10 border border-green-500/30 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <CreditCard className="w-4 h-4 text-green-400" />
              <span className="text-green-400 font-medium text-sm">Send Money To:</span>
            </div>
            <div className="text-center">
              <p className="text-white font-bold text-lg">01XXXXXXXXX</p>
              <p className="text-xs text-gray-400">Official Vegas Ace Slots Bkash</p>
            </div>
          </div>

          {/* Buttons */}
          <div className="flex space-x-3 pt-4">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-700"
                disabled={loading}
              >
                Cancel
              </Button>
            )}
            <Button
              type="submit"
              className="flex-1 bg-casino-gold text-black hover:bg-casino-gold/90 font-bold"
              disabled={loading}
            >
              {loading ? (
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-black/30 border-t-black rounded-full animate-spin"></div>
                  <span>Submitting...</span>
                </div>
              ) : (
                "Submit Deposit Request"
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
