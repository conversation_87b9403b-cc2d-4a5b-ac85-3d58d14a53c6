import {
  users,
  gameSessions,
  spinResults,
  depositRequests,
  withdrawalRequests,
  type User,
  type InsertUser,
  type GameSession,
  type InsertGameSession,
  type SpinResult,
  type InsertSpinResult,
  type DepositRequest,
  type InsertDepositRequest,
  type WithdrawalRequest,
  type InsertWithdrawalRequest
} from "@shared/schema";
import { db } from "./db";
import { eq, desc, sum, count } from "drizzle-orm";
import session from "express-session";
import createMemoryStore from "memorystore";
import { randomBytes, createHash } from "crypto";
import { SlotPsychologyEngine } from "./psychology-engine";
import { EnhancedPsychologyEngine } from "./enhanced-psychology-engine";
import { superAceWinCalculator } from "./super-ace-win-calculator";

const MemoryStore = createMemoryStore(session);

export interface IStorage {
  getUser(id: number): Promise<User | undefined>;
  getUserByMobile(mobile: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUserLastLogin(userId: number): Promise<void>;
  updateUserBalance(userId: number, newBalance: string): Promise<void>;
  getUserStats(userId: number): Promise<any>;

  createGameSession(session: InsertGameSession): Promise<GameSession>;
  getGameSession(sessionId: string): Promise<GameSession | undefined>;
  updateSessionBalance(sessionId: string, balance: string): Promise<void>;
  getSessionStats(sessionId: string): Promise<any>;

  processSpin(params: {
    sessionId: string;
    userId: number;
    betAmount: string;
    isFreeSpinRound: boolean;
    session: GameSession;
  }): Promise<any>;

  // Admin methods
  getAllUsers(): Promise<User[]>;
  toggleUserStatus(userId: number, isActive: boolean): Promise<void>;

  // Deposit methods
  createDepositRequest(request: InsertDepositRequest): Promise<DepositRequest>;
  getDepositRequests(status?: string): Promise<DepositRequest[]>;
  processDepositRequest(requestId: number, status: 'approved' | 'rejected', adminId: number, notes?: string): Promise<void>;

  // Withdrawal methods
  createWithdrawalRequest(request: InsertWithdrawalRequest): Promise<WithdrawalRequest>;
  getWithdrawalRequests(status?: string): Promise<WithdrawalRequest[]>;
  processWithdrawalRequest(requestId: number, status: 'completed' | 'rejected', adminId: number, notes?: string): Promise<void>;

  // Admin dashboard stats
  getAdminStats(): Promise<any>;

  // 🎁 Daily bonuses and engagement
  checkDailyLogin(userId: number): Promise<{ hasBonus: boolean; amount?: number; streak?: number }>;
  claimDailyBonus(userId: number): Promise<{ amount: number; newStreak: number }>;
  checkFreeSpins(userId: number): Promise<{ hasFreeSpin: boolean; remaining?: number }>;
  claimFreeSpin(userId: number): Promise<{ success: boolean; remaining: number }>;

  sessionStore: any;
}

// Crypto-secure RNG implementation
class CryptoRNG {
  private seed: string;
  private counter: number;

  constructor(seed: string) {
    this.seed = seed;
    this.counter = 0;
  }

  next(): number {
    this.counter++;
    const input = this.seed + this.counter.toString();
    const hash = createHash('sha256').update(input).digest();

    // Convert first 4 bytes to number and normalize to [0, 1)
    const value = hash.readUInt32BE(0) / 0xffffffff;
    return value;
  }

  nextInt(min: number, max: number): number {
    return Math.floor(this.next() * (max - min + 1)) + min;
  }

  reseed() {
    this.seed = randomBytes(32).toString('hex');
    this.counter = 0;
  }
}

// Symbol definitions with realistic casino weights
const symbols = {
  '9': { weight: 35, payout: [0, 0, 2, 10, 50], rarity: 'common' },
  '10': { weight: 30, payout: [0, 0, 3, 15, 75], rarity: 'common' },
  'J': { weight: 25, payout: [0, 0, 4, 20, 100], rarity: 'uncommon' },
  'Q': { weight: 20, payout: [0, 0, 5, 25, 125], rarity: 'uncommon' },
  'K': { weight: 15, payout: [0, 0, 6, 30, 150], rarity: 'rare' },
  'A': { weight: 12, payout: [0, 0, 8, 40, 200], rarity: 'rare' },
  'SCATTER': { weight: 3, payout: [0, 0, 2, 8, 75], rarity: 'scatter' },
  'WILD': { weight: 2, payout: [0, 0, 20, 75, 300], rarity: 'wild' }
};

export class DatabaseStorage implements IStorage {
  sessionStore: any;
  private enhancedPsychology: EnhancedPsychologyEngine;

  constructor() {
    this.sessionStore = new MemoryStore({
      checkPeriod: 86400000, // 24 hours
    });
    this.enhancedPsychology = new EnhancedPsychologyEngine();
  }

  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || undefined;
  }

  async getUserByMobile(mobile: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.mobile, mobile));
    return user || undefined;
  }



  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values(insertUser)
      .returning();
    return user;
  }

  async updateUserLastLogin(userId: number): Promise<void> {
    await db
      .update(users)
      .set({ lastLogin: new Date() })
      .where(eq(users.id, userId));
  }

  async updateUserBalance(userId: number, newBalance: string): Promise<void> {
    await db
      .update(users)
      .set({ balance: newBalance })
      .where(eq(users.id, userId));
  }

  async getUserStats(userId: number): Promise<any> {
    const user = await this.getUser(userId);
    if (!user) throw new Error("User not found");

    const sessionStats = await db
      .select({
        totalSessions: count(),
        totalBet: sum(gameSessions.totalBet),
        totalWon: sum(gameSessions.totalWon),
      })
      .from(gameSessions)
      .where(eq(gameSessions.userId, userId));

    return {
      user,
      sessions: sessionStats[0] || { totalSessions: 0, totalBet: "0", totalWon: "0" }
    };
  }

  async createGameSession(session: InsertGameSession): Promise<GameSession> {
    const [newSession] = await db
      .insert(gameSessions)
      .values(session)
      .returning();
    return newSession;
  }

  async getGameSession(sessionId: string): Promise<GameSession | undefined> {
    const [session] = await db
      .select()
      .from(gameSessions)
      .where(eq(gameSessions.sessionId, sessionId));
    return session || undefined;
  }

  async updateSessionBalance(sessionId: string, balance: string): Promise<void> {
    await db
      .update(gameSessions)
      .set({
        currentBalance: balance,
        updatedAt: new Date()
      })
      .where(eq(gameSessions.sessionId, sessionId));
  }

  async getSessionStats(sessionId: string): Promise<any> {
    const spinStats = await db
      .select({
        totalSpins: count(),
        totalBet: sum(spinResults.betAmount),
        totalWon: sum(spinResults.winAmount),
      })
      .from(spinResults)
      .where(eq(spinResults.sessionId, sessionId));

    return spinStats[0] || { totalSpins: 0, totalBet: "0", totalWon: "0" };
  }

  // 🧠 Enhanced spin processing with natural game feel and psychology
  async processSpin(params: {
    sessionId: string;
    userId: number;
    betAmount: string;
    isFreeSpinRound: boolean;
    session: GameSession;
  }): Promise<any> {
    const { sessionId, userId, betAmount, isFreeSpinRound, session } = params;
    const bet = parseFloat(betAmount);

    // Get user data for psychology calculations
    const user = await this.getUser(userId);
    if (!user) throw new Error("User not found");

    // 🎯 Calculate dynamic win chance based on enhanced psychology
    const dynamicWinChance = this.enhancedPsychology.calculateDynamicWinChance(user, bet);
    const bonusChance = this.enhancedPsychology.calculateBonusChance(user, bet);

    // Initialize RNG with session seed
    const rng = new CryptoRNG(session.rngSeed);

    // 🎲 Determine if this spin should win
    const shouldWin = rng.next() < dynamicWinChance;
    const shouldBonus = rng.next() < bonusChance;
    const shouldNearMiss = !shouldWin && this.enhancedPsychology.shouldCreateNearMiss(user);

    // Generate grid based on outcome
    let grid: string[][] = [];
    let wins: any[] = [];
    let totalWin = 0;
    let scatterTriggered = false;
    let bonusTriggered = false;

    if (shouldWin || shouldBonus) {
      // Generate winning grid
      grid = this.generateWinningGrid(rng, shouldBonus);
      wins = this.calculateWins(grid, bet);

      // Calculate win amount using the new calculator result
      const winResult = superAceWinCalculator.calculateWins(grid, bet, 1);
      totalWin = winResult.totalPayout;

      if (shouldBonus || winResult.hasBonus) {
        bonusTriggered = true;
        scatterTriggered = true;
      }
    } else if (shouldNearMiss) {
      // Generate near-miss grid (2 scatters, almost jackpot, etc.)
      grid = this.generateNearMissGrid(rng);
      wins = this.calculateWins(grid, bet);
    } else {
      // Generate losing grid
      grid = this.generateLosingGrid(rng);
      wins = this.calculateWins(grid, bet);
    }

    // Apply current multiplier
    const multiplier = session.currentMultiplier || 1;
    totalWin *= multiplier;

    // Deduct bet (unless free spin)
    let newBalance = parseFloat(session.currentBalance);
    if (!isFreeSpinRound) {
      newBalance -= bet;
    }

    // Add winnings
    newBalance += totalWin;

    // 🧠 Update user psychology after spin
    const psychologyUpdates = this.enhancedPsychology.updateUserPsychology(
      user,
      totalWin > 0,
      totalWin,
      bet
    );

    // Update user with psychology data
    if (Object.keys(psychologyUpdates).length > 0) {
      await db
        .update(users)
        .set(psychologyUpdates)
        .where(eq(users.id, userId));
    }

    // Update session
    await db
      .update(gameSessions)
      .set({
        currentBalance: newBalance.toFixed(2),
        totalBet: isFreeSpinRound ? session.totalBet : (parseFloat(session.totalBet) + bet).toFixed(2),
        totalWon: (parseFloat(session.totalWon) + totalWin).toFixed(2),
        spinsCount: session.spinsCount + 1,
        freeSpinsRemaining: bonusTriggered ? 10 : Math.max(0, session.freeSpinsRemaining - (isFreeSpinRound ? 1 : 0)),
        currentMultiplier: bonusTriggered ? 2 : (session.freeSpinsRemaining > 0 ? session.currentMultiplier : 1),
        updatedAt: new Date()
      })
      .where(eq(gameSessions.sessionId, sessionId));

    // Update user balance and total spins
    await db
      .update(users)
      .set({
        balance: newBalance.toFixed(2),
        totalSpins: user.totalSpins + 1,
        totalWins: totalWin > 0 ? (parseFloat(user.totalWins) + totalWin).toFixed(2) : user.totalWins
      })
      .where(eq(users.id, userId));

    // Record spin result
    const spinResult = await db
      .insert(spinResults)
      .values({
        sessionId,
        userId,
        betAmount,
        winAmount: totalWin.toFixed(2),
        symbols: grid,
        winLines: wins,
        multiplier,
        isFreeSpinRound,
        scatterTriggered,
        bonusTriggered,
        rngValues: [], // We'll add this if needed
      })
      .returning();

    return {
      grid,
      wins,
      totalWin: totalWin.toFixed(2),
      newBalance: newBalance.toFixed(2),
      multiplier,
      scatterTriggered,
      bonusTriggered,
      freeSpinsRemaining: bonusTriggered ? 10 : Math.max(0, session.freeSpinsRemaining - (isFreeSpinRound ? 1 : 0)),
      spinId: spinResult[0].id,
      // 🎭 Enhanced psychology data
      isNearMiss: shouldNearMiss,
      dynamicWinChance: Math.round(dynamicWinChance * 100), // For debugging
      luckScore: psychologyUpdates.luckScore || user.luckScore,
      vipLevel: psychologyUpdates.vipLevel || user.vipLevel,
      // 🎯 Big win celebration trigger
      isBigWin: totalWin >= bet * 5,
      isMegaWin: totalWin >= bet * 10,
    };
  }

  // Super Ace Style 1024 ways to win calculation
  private calculateWins(grid: string[][], betAmount: number): any {
    // Use the new Super Ace win calculator
    const result = superAceWinCalculator.calculateWins(grid, betAmount, 1);
    return result.wins; // Return just the wins array for compatibility
  }

  private findWinPath(grid: string[][], targetSymbol: string, reel: number, row: number): any[] {
    const path = [{ reel, row }];

    for (let nextReel = reel + 1; nextReel < 5; nextReel++) {
      let foundMatch = false;

      for (let nextRow = 0; nextRow < 4; nextRow++) {
        const symbol = grid[nextReel][nextRow];
        if (symbol === targetSymbol || symbol === 'WILD' || targetSymbol === 'WILD') {
          path.push({ reel: nextReel, row: nextRow });
          foundMatch = true;
          break;
        }
      }

      if (!foundMatch) break;
    }

    return path;
  }

  private findScatters(grid: string[][]): any[] {
    const scatters: any[] = [];
    for (let reel = 0; reel < 5; reel++) {
      for (let row = 0; row < 4; row++) {
        if (grid[reel][row] === 'SCATTER') {
          scatters.push({ reel, row });
        }
      }
    }
    return scatters;
  }

  // 🎰 Generate winning grid with guaranteed wins
  private generateWinningGrid(rng: CryptoRNG, shouldBonus: boolean): string[][] {
    const grid: string[][] = [];
    const symbolKeys = Object.keys(symbols).filter(s => s !== 'SCATTER' && s !== 'WILD');

    // Pick a winning symbol
    const winSymbol = symbolKeys[Math.floor(rng.next() * symbolKeys.length)];

    for (let reel = 0; reel < 5; reel++) {
      grid[reel] = [];
      for (let row = 0; row < 4; row++) {
        if (reel < 3 && row === 0) {
          // Guarantee winning line on top row for first 3 reels
          grid[reel][row] = winSymbol;
        } else if (shouldBonus && reel < 3 && row === 1) {
          // Add scatters for bonus
          grid[reel][row] = 'SCATTER';
        } else {
          // Fill with random symbols
          const allSymbols = Object.keys(symbols);
          grid[reel][row] = allSymbols[Math.floor(rng.next() * allSymbols.length)];
        }
      }
    }

    return grid;
  }

  // 🎯 Generate near-miss grid (psychological hook)
  private generateNearMissGrid(rng: CryptoRNG): string[][] {
    const grid: string[][] = [];

    for (let reel = 0; reel < 5; reel++) {
      grid[reel] = [];
      for (let row = 0; row < 4; row++) {
        if (reel < 2 && row === 1) {
          // Place 2 scatters to create "almost bonus" feeling
          grid[reel][row] = 'SCATTER';
        } else if (reel === 2 && row === 1) {
          // Third scatter just misses
          grid[reel][row] = Math.random() < 0.5 ? 'WILD' : 'A';
        } else {
          // Fill with random symbols
          const allSymbols = Object.keys(symbols);
          grid[reel][row] = allSymbols[Math.floor(rng.next() * allSymbols.length)];
        }
      }
    }

    return grid;
  }

  // 💸 Generate losing grid
  private generateLosingGrid(rng: CryptoRNG): string[][] {
    const grid: string[][] = [];
    const symbolKeys = Object.keys(symbols);

    for (let reel = 0; reel < 5; reel++) {
      grid[reel] = [];
      for (let row = 0; row < 4; row++) {
        // Ensure no winning combinations
        let symbol;
        do {
          symbol = symbolKeys[Math.floor(rng.next() * symbolKeys.length)];
        } while (this.wouldCreateWin(grid, reel, row, symbol));

        grid[reel][row] = symbol;
      }
    }

    return grid;
  }

  // Helper to check if placing a symbol would create a win
  private wouldCreateWin(grid: string[][], reel: number, row: number, symbol: string): boolean {
    if (reel < 2) return false; // First two reels can't create wins yet

    // Check if this would complete a 3+ symbol line
    if (reel >= 2) {
      for (let checkRow = 0; checkRow < 4; checkRow++) {
        if (grid[0] && grid[1] &&
            grid[0][checkRow] === symbol &&
            grid[1][checkRow] === symbol) {
          return true; // Would create a 3-symbol win
        }
      }
    }

    return false;
  }

  // Admin methods implementation
  async getAllUsers(): Promise<User[]> {
    return await db.select().from(users).orderBy(desc(users.joinDate));
  }

  async toggleUserStatus(userId: number, isActive: boolean): Promise<void> {
    await db
      .update(users)
      .set({ isActive })
      .where(eq(users.id, userId));
  }

  // Deposit methods
  async createDepositRequest(request: InsertDepositRequest): Promise<DepositRequest> {
    const [depositRequest] = await db
      .insert(depositRequests)
      .values(request)
      .returning();
    return depositRequest;
  }

  async getDepositRequests(status?: string): Promise<DepositRequest[]> {
    if (status) {
      return await db
        .select()
        .from(depositRequests)
        .where(eq(depositRequests.status, status))
        .orderBy(desc(depositRequests.createdAt));
    }
    return await db
      .select()
      .from(depositRequests)
      .orderBy(desc(depositRequests.createdAt));
  }

  async processDepositRequest(requestId: number, status: 'approved' | 'rejected', adminId: number, notes?: string): Promise<void> {
    const [request] = await db
      .select()
      .from(depositRequests)
      .where(eq(depositRequests.id, requestId));

    if (!request) throw new Error("Deposit request not found");

    // Update request status
    await db
      .update(depositRequests)
      .set({
        status,
        adminNotes: notes,
        processedBy: adminId,
        processedAt: new Date()
      })
      .where(eq(depositRequests.id, requestId));

    // If approved, credit user balance
    if (status === 'approved') {
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.id, request.userId));

      if (user) {
        const newBalance = (parseFloat(user.balance) + parseFloat(request.amount)).toFixed(2);
        await this.updateUserBalance(request.userId, newBalance);

        // Update total deposits
        const newTotalDeposits = (parseFloat(user.totalDeposits) + parseFloat(request.amount)).toFixed(2);
        await db
          .update(users)
          .set({ totalDeposits: newTotalDeposits })
          .where(eq(users.id, request.userId));
      }
    }
  }

  // Withdrawal methods
  async createWithdrawalRequest(request: InsertWithdrawalRequest): Promise<WithdrawalRequest> {
    // First, deduct the amount from user balance
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.id, request.userId));

    if (!user) throw new Error("User not found");

    const currentBalance = parseFloat(user.balance);
    const withdrawAmount = parseFloat(request.amount.toString());

    if (currentBalance < withdrawAmount) {
      throw new Error("Insufficient balance");
    }

    // Deduct amount from balance
    const newBalance = (currentBalance - withdrawAmount).toFixed(2);
    await this.updateUserBalance(request.userId, newBalance);

    // Create withdrawal request
    const [withdrawalRequest] = await db
      .insert(withdrawalRequests)
      .values(request)
      .returning();

    return withdrawalRequest;
  }

  async getWithdrawalRequests(status?: string): Promise<WithdrawalRequest[]> {
    if (status) {
      return await db
        .select()
        .from(withdrawalRequests)
        .where(eq(withdrawalRequests.status, status))
        .orderBy(desc(withdrawalRequests.createdAt));
    }
    return await db
      .select()
      .from(withdrawalRequests)
      .orderBy(desc(withdrawalRequests.createdAt));
  }

  async processWithdrawalRequest(requestId: number, status: 'completed' | 'rejected', adminId: number, notes?: string): Promise<void> {
    const [request] = await db
      .select()
      .from(withdrawalRequests)
      .where(eq(withdrawalRequests.id, requestId));

    if (!request) throw new Error("Withdrawal request not found");

    // Update request status
    await db
      .update(withdrawalRequests)
      .set({
        status,
        adminNotes: notes,
        processedBy: adminId,
        processedAt: new Date()
      })
      .where(eq(withdrawalRequests.id, requestId));

    // If rejected, return money to user balance
    if (status === 'rejected') {
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.id, request.userId));

      if (user) {
        const newBalance = (parseFloat(user.balance) + parseFloat(request.amount)).toFixed(2);
        await this.updateUserBalance(request.userId, newBalance);
      }
    } else if (status === 'completed') {
      // Update total withdrawals
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.id, request.userId));

      if (user) {
        const newTotalWithdrawals = (parseFloat(user.totalWithdrawals) + parseFloat(request.amount)).toFixed(2);
        await db
          .update(users)
          .set({ totalWithdrawals: newTotalWithdrawals })
          .where(eq(users.id, request.userId));
      }
    }
  }

  async getAdminStats(): Promise<any> {
    const totalUsers = await db.select({ count: count() }).from(users);
    const activeUsers = await db.select({ count: count() }).from(users).where(eq(users.isActive, true));

    const pendingDeposits = await db
      .select({ count: count(), total: sum(depositRequests.amount) })
      .from(depositRequests)
      .where(eq(depositRequests.status, 'pending'));

    const pendingWithdrawals = await db
      .select({ count: count(), total: sum(withdrawalRequests.amount) })
      .from(withdrawalRequests)
      .where(eq(withdrawalRequests.status, 'pending'));

    const totalBalance = await db.select({ total: sum(users.balance) }).from(users);

    return {
      totalUsers: totalUsers[0]?.count || 0,
      activeUsers: activeUsers[0]?.count || 0,
      pendingDeposits: {
        count: pendingDeposits[0]?.count || 0,
        total: pendingDeposits[0]?.total || "0"
      },
      pendingWithdrawals: {
        count: pendingWithdrawals[0]?.count || 0,
        total: pendingWithdrawals[0]?.total || "0"
      },
      totalBalance: totalBalance[0]?.total || "0"
    };
  }

  // 🎁 Daily Login Bonus System
  async checkDailyLogin(userId: number): Promise<{ hasBonus: boolean; amount?: number; streak?: number }> {
    const user = await this.getUser(userId);
    if (!user) throw new Error("User not found");

    const today = new Date().toISOString().split('T')[0];
    const lastLoginDate = user.lastLoginDate;

    if (lastLoginDate === today) {
      // Already claimed today
      return { hasBonus: false };
    }

    // Calculate new streak
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStr = yesterday.toISOString().split('T')[0];

    let newStreak = 1;
    if (lastLoginDate === yesterdayStr) {
      // Consecutive day
      newStreak = (user.loginStreak || 0) + 1;
    }

    const bonusAmount = this.enhancedPsychology.calculateLoginBonus(newStreak);

    return {
      hasBonus: true,
      amount: bonusAmount,
      streak: newStreak
    };
  }

  async claimDailyBonus(userId: number): Promise<{ amount: number; newStreak: number }> {
    const bonusCheck = await this.checkDailyLogin(userId);
    if (!bonusCheck.hasBonus) {
      throw new Error("No daily bonus available");
    }

    const user = await this.getUser(userId);
    if (!user) throw new Error("User not found");

    const today = new Date().toISOString().split('T')[0];
    const bonusAmount = bonusCheck.amount!;
    const newStreak = bonusCheck.streak!;

    // Update user
    const newBalance = (parseFloat(user.balance) + bonusAmount).toFixed(2);

    await db
      .update(users)
      .set({
        balance: newBalance,
        loginStreak: newStreak,
        lastLoginDate: today
      })
      .where(eq(users.id, userId));

    return {
      amount: bonusAmount,
      newStreak
    };
  }

  // 🎰 Free Spins System (10 days for new users)
  async checkFreeSpins(userId: number): Promise<{ hasFreeSpin: boolean; remaining?: number }> {
    const user = await this.getUser(userId);
    if (!user) throw new Error("User not found");

    return {
      hasFreeSpin: this.enhancedPsychology.shouldGetFreeSpinToday(user),
      remaining: user.freeSpinsRemaining || 0
    };
  }

  async claimFreeSpin(userId: number): Promise<{ success: boolean; remaining: number }> {
    const user = await this.getUser(userId);
    if (!user) throw new Error("User not found");

    if (!this.enhancedPsychology.shouldGetFreeSpinToday(user)) {
      return { success: false, remaining: user.freeSpinsRemaining || 0 };
    }

    const today = new Date().toISOString().split('T')[0];
    const newRemaining = Math.max(0, (user.freeSpinsRemaining || 0) - 1);

    await db
      .update(users)
      .set({
        freeSpinsRemaining: newRemaining,
        lastFreeSpinDate: today
      })
      .where(eq(users.id, userId));

    return {
      success: true,
      remaining: newRemaining
    };
  }
}

export const storage = new DatabaseStorage();
