import { useAuth } from "@/hooks/use-auth";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { useLocation } from "wouter";
import { useState } from "react";
import { DepositForm } from "@/components/forms/deposit-form";
import { WithdrawalForm } from "@/components/forms/withdrawal-form";

export default function ProfilePage() {
  const { user } = useAuth();
  const [, setLocation] = useLocation();
  const [showDepositForm, setShowDepositForm] = useState(false);
  const [showWithdrawalForm, setShowWithdrawalForm] = useState(false);

  console.log('ProfilePage - user:', user);
  console.log('ProfilePage - useAuth hook result:', { user });

  if (!user) {
    console.log('ProfilePage - no user, returning null');
    return (
      <div style={{
        minHeight: '100vh',
        background: '#ff0000',
        color: 'white',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '2rem'
      }}>
        NO USER - REDIRECTING...
      </div>
    );
  }

  console.log('✅ ProfilePage - rendering with user:', user);
  console.log('💰 ProfilePage - user.balance:', user.balance);
  console.log('🔍 ProfilePage - user.balance type:', typeof user.balance);

  // Safe data handling - PostgreSQL decimals come as strings
  const safeBalance = typeof user.balance === 'number' ? user.balance : parseFloat(user.balance) || 0;
  const safeName = user.name || 'Player';
  const safeInitial = safeName.charAt(0).toUpperCase() || 'P';

  console.log('✅ Safe balance converted:', safeBalance, typeof safeBalance);

  // Full profile page
  return (
    <div
      style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)',
        color: 'white',
        fontFamily: 'Inter, sans-serif'
      }}
    >
      {/* Header */}
      <div style={{ padding: '20px', borderBottom: '1px solid rgba(255, 215, 0, 0.3)' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Button
            variant="outline"
            onClick={() => setLocation("/")}
            style={{
              background: 'rgba(255, 215, 0, 0.1)',
              border: '1px solid rgba(255, 215, 0, 0.3)',
              color: '#FFD700',
              padding: '10px 20px'
            }}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Game
          </Button>

          <div style={{ textAlign: 'center' }}>
            <h1 style={{
              fontSize: '2.5rem',
              fontWeight: 'bold',
              background: 'linear-gradient(45deg, #FFD700, #FFA500)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              margin: 0
            }}>
              🎰 Player Profile 🎰
            </h1>
            <p style={{ color: '#ccc', fontSize: '14px', margin: '5px 0 0 0' }}>
              Manage your account & winnings
            </p>
          </div>

          <div style={{ width: '140px' }}></div>
        </div>
      </div>

      {/* Main Content */}
      <div style={{ padding: '30px 20px' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>

          {/* User Info Card */}
          <div style={{
            background: 'rgba(255, 255, 255, 0.05)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 215, 0, 0.3)',
            borderRadius: '15px',
            padding: '30px',
            marginBottom: '30px',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '30px', flexWrap: 'wrap' }}>

              {/* Avatar */}
              <div style={{
                width: '100px',
                height: '100px',
                background: 'linear-gradient(135deg, #8A2BE2, #4B0082)',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                border: '3px solid rgba(255, 215, 0, 0.5)',
                fontSize: '2.5rem',
                fontWeight: 'bold',
                color: 'white'
              }}>
                {safeInitial}
              </div>

              {/* User Info */}
              <div style={{ flex: 1, minWidth: '200px' }}>
                <h2 style={{
                  fontSize: '2rem',
                  fontWeight: 'bold',
                  color: 'white',
                  margin: '0 0 10px 0'
                }}>
                  {safeName}
                </h2>
                <p style={{
                  color: '#FFD700',
                  fontSize: '16px',
                  margin: '5px 0',
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  👑 Premium Player
                </p>
                <p style={{ color: '#ccc', fontSize: '14px', margin: '5px 0' }}>
                  Member since {new Date().getFullYear()}
                </p>
              </div>

              {/* Balance */}
              <div style={{
                background: 'rgba(0, 255, 0, 0.1)',
                border: '1px solid rgba(0, 255, 0, 0.3)',
                borderRadius: '10px',
                padding: '20px',
                textAlign: 'center',
                minWidth: '200px'
              }}>
                <p style={{ color: '#00FF00', fontSize: '14px', margin: '0 0 10px 0' }}>
                  Current Balance
                </p>
                <p style={{
                  fontSize: '2.5rem',
                  fontWeight: 'bold',
                  color: 'white',
                  margin: 0,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  💰 ${safeBalance.toFixed(2)}
                </p>
              </div>
            </div>
          </div>

          {/* Stats Grid */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '20px',
            marginBottom: '30px'
          }}>

            {/* Total Winnings */}
            <div style={{
              background: 'rgba(138, 43, 226, 0.1)',
              border: '1px solid rgba(138, 43, 226, 0.3)',
              borderRadius: '10px',
              padding: '25px',
              textAlign: 'center'
            }}>
              <div style={{ fontSize: '3rem', marginBottom: '10px' }}>📈</div>
              <p style={{ fontSize: '1.8rem', fontWeight: 'bold', color: 'white', margin: '10px 0' }}>
                $2,450
              </p>
              <p style={{ color: '#ccc', fontSize: '14px', margin: 0 }}>
                Total Winnings
              </p>
            </div>

            {/* Games Played */}
            <div style={{
              background: 'rgba(255, 215, 0, 0.1)',
              border: '1px solid rgba(255, 215, 0, 0.3)',
              borderRadius: '10px',
              padding: '25px',
              textAlign: 'center'
            }}>
              <div style={{ fontSize: '3rem', marginBottom: '10px' }}>🏆</div>
              <p style={{ fontSize: '1.8rem', fontWeight: 'bold', color: 'white', margin: '10px 0' }}>
                127
              </p>
              <p style={{ color: '#ccc', fontSize: '14px', margin: 0 }}>
                Games Played
              </p>
            </div>

            {/* Biggest Win */}
            <div style={{
              background: 'rgba(220, 20, 60, 0.1)',
              border: '1px solid rgba(220, 20, 60, 0.3)',
              borderRadius: '10px',
              padding: '25px',
              textAlign: 'center'
            }}>
              <div style={{ fontSize: '3rem', marginBottom: '10px' }}>⭐</div>
              <p style={{ fontSize: '1.8rem', fontWeight: 'bold', color: 'white', margin: '10px 0' }}>
                $850
              </p>
              <p style={{ color: '#ccc', fontSize: '14px', margin: 0 }}>
                Biggest Win
              </p>
            </div>

            {/* Friends Invited */}
            <div style={{
              background: 'rgba(0, 255, 0, 0.1)',
              border: '1px solid rgba(0, 255, 0, 0.3)',
              borderRadius: '10px',
              padding: '25px',
              textAlign: 'center'
            }}>
              <div style={{ fontSize: '3rem', marginBottom: '10px' }}>👥</div>
              <p style={{ fontSize: '1.8rem', fontWeight: 'bold', color: 'white', margin: '10px 0' }}>
                5
              </p>
              <p style={{ color: '#ccc', fontSize: '14px', margin: 0 }}>
                Friends Invited
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '20px'
          }}>
            <button
              onClick={() => setShowDepositForm(true)}
              style={{
                background: 'linear-gradient(135deg, #00FF00, #32CD32)',
                border: 'none',
                borderRadius: '10px',
                padding: '20px',
                color: 'white',
                fontSize: '18px',
                fontWeight: 'bold',
                cursor: 'pointer',
                transition: 'transform 0.2s'
              }}
              onMouseOver={(e) => e.target.style.transform = 'scale(1.05)'}
              onMouseOut={(e) => e.target.style.transform = 'scale(1)'}
            >
              💰 Deposit Funds
            </button>

            <button
              onClick={() => setShowWithdrawalForm(true)}
              style={{
                background: 'linear-gradient(135deg, #FF6347, #DC143C)',
                border: 'none',
                borderRadius: '10px',
                padding: '20px',
                color: 'white',
                fontSize: '18px',
                fontWeight: 'bold',
                cursor: 'pointer',
                transition: 'transform 0.2s'
              }}
              onMouseOver={(e) => e.target.style.transform = 'scale(1.05)'}
              onMouseOut={(e) => e.target.style.transform = 'scale(1)'}
            >
              💸 Withdraw
            </button>

            <button style={{
              background: 'linear-gradient(135deg, #8A2BE2, #4B0082)',
              border: 'none',
              borderRadius: '10px',
              padding: '20px',
              color: 'white',
              fontSize: '18px',
              fontWeight: 'bold',
              cursor: 'pointer',
              transition: 'transform 0.2s'
            }}
            onMouseOver={(e) => e.target.style.transform = 'scale(1.05)'}
            onMouseOut={(e) => e.target.style.transform = 'scale(1)'}
            >
              👥 Invite Friends
            </button>

            <button style={{
              background: 'linear-gradient(135deg, #FFD700, #FFA500)',
              border: 'none',
              borderRadius: '10px',
              padding: '20px',
              color: '#000',
              fontSize: '18px',
              fontWeight: 'bold',
              cursor: 'pointer',
              transition: 'transform 0.2s'
            }}
            onMouseOver={(e) => e.target.style.transform = 'scale(1.05)'}
            onMouseOut={(e) => e.target.style.transform = 'scale(1)'}
            >
              📊 View History
            </button>
          </div>
        </div>
      </div>

      {/* Deposit Form Modal */}
      {showDepositForm && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000,
          padding: '20px'
        }}>
          <DepositForm
            onSuccess={() => {
              setShowDepositForm(false);
              // Refresh user data
              window.location.reload();
            }}
            onCancel={() => setShowDepositForm(false)}
          />
        </div>
      )}

      {/* Withdrawal Form Modal */}
      {showWithdrawalForm && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000,
          padding: '20px'
        }}>
          <WithdrawalForm
            onSuccess={() => {
              setShowWithdrawalForm(false);
              // Refresh user data
              window.location.reload();
            }}
            onCancel={() => setShowWithdrawalForm(false)}
          />
        </div>
      )}
    </div>
  );
}
