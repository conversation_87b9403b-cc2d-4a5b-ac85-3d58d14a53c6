/**
 * Super Ace Style 1024 Ways-to-Win Calculator - Server Side
 * Fixed implementation with proper reel behavior and win calculation
 */

interface WinPosition {
  reel: number;
  row: number;
}

interface WinLine {
  symbol: string;
  positions: WinPosition[];
  count: number;
  payout: number;
  ways: number;
  type: 'ways' | 'scatter';
}

interface WinResult {
  wins: WinLine[];
  totalPayout: number;
  scatterCount: number;
  hasBonus: boolean;
  hasFreeSpins: boolean;
  freeSpinsAwarded: number;
}

// Super Ace Style Paytable
const PAYTABLE = {
  'A': { 3: 50, 4: 100, 5: 200 },
  'K': { 3: 40, 4: 80, 5: 160 },
  'Q': { 3: 30, 4: 60, 5: 120 },
  'J': { 3: 20, 4: 40, 5: 80 },
  '10': { 3: 10, 4: 20, 5: 40 },
  '9': { 3: 5, 4: 15, 5: 30 },
  'WILD': { 3: 100, 4: 200, 5: 500 },
  'SCATTER': { 3: 2, 4: 5, 5: 20 } // Multiplies total bet
};

export class SuperAceWinCalculator {
  private readonly REELS = 5;
  private readonly ROWS = 4;
  private readonly MIN_WIN_LENGTH = 3;

  /**
   * Calculate all wins in the symbol grid - Super Ace Style
   */
  public calculateWins(
    symbolGrid: string[][],
    betAmount: number,
    multiplier: number = 1
  ): WinResult {
    if (!this.validateGrid(symbolGrid)) {
      throw new Error('Invalid symbol grid provided');
    }

    const wins: WinLine[] = [];

    // 1. Calculate scatter wins FIRST (any position, multiplies total bet)
    const scatterWins = this.calculateScatterWins(symbolGrid, betAmount);
    wins.push(...scatterWins);

    // 2. Calculate 1024 ways-to-win (left to right, consecutive reels)
    const waysWins = this.calculate1024Ways(symbolGrid, betAmount);
    wins.push(...waysWins);

    // Apply multiplier to all wins
    const adjustedWins = wins.map(win => ({
      ...win,
      payout: win.payout * multiplier
    }));

    // Calculate total payout
    const totalPayout = adjustedWins.reduce((sum, win) => sum + win.payout, 0);

    // Check for bonus features
    const scatterCount = this.countScatters(symbolGrid);
    const hasBonus = scatterCount >= 3;
    const hasFreeSpins = scatterCount >= 3;
    const freeSpinsAwarded = scatterCount >= 3 ? 10 : 0;

    return {
      wins: adjustedWins,
      totalPayout,
      scatterCount,
      hasBonus,
      hasFreeSpins,
      freeSpinsAwarded
    };
  }

  /**
   * Calculate 1024 ways-to-win - Super Ace Style
   */
  private calculate1024Ways(symbolGrid: string[][], betAmount: number): WinLine[] {
    const wins: WinLine[] = [];
    const symbolWins = new Map<string, { positions: WinPosition[], ways: number }>();

    // For each starting position in reel 0
    for (let startRow = 0; startRow < this.ROWS; startRow++) {
      const startSymbol = symbolGrid[0][startRow];
      
      if (!startSymbol || startSymbol === 'SCATTER') {
        continue; // Skip empty positions and scatters
      }

      // Find all possible paths from this starting position
      const paths = this.findAllWinPaths(symbolGrid, startSymbol, 0, startRow);
      
      for (const path of paths) {
        if (path.length >= this.MIN_WIN_LENGTH) {
          const key = `${startSymbol}-${path.length}`;
          
          if (!symbolWins.has(key)) {
            symbolWins.set(key, { positions: [], ways: 0 });
          }
          
          const winData = symbolWins.get(key)!;
          winData.positions.push(...path);
          winData.ways++;
        }
      }
    }

    // Convert to win lines
    symbolWins.forEach((winData, key) => {
      const [symbol, countStr] = key.split('-');
      const count = parseInt(countStr);
      const basePayout = this.getSymbolPayout(symbol, count);
      
      if (basePayout > 0) {
        // Calculate bet per way (total bet / 1024 ways)
        const betPerWay = betAmount / 1024;
        const totalPayout = basePayout * winData.ways * betPerWay;
        
        wins.push({
          symbol,
          positions: winData.positions,
          count,
          ways: winData.ways,
          payout: totalPayout,
          type: 'ways'
        });
      }
    });

    return wins;
  }

  /**
   * Find all possible winning paths from a starting position
   */
  private findAllWinPaths(
    symbolGrid: string[][],
    targetSymbol: string,
    startReel: number,
    startRow: number
  ): WinPosition[][] {
    const allPaths: WinPosition[][] = [];
    
    const findPaths = (reel: number, currentPath: WinPosition[]): void => {
      // If we've reached the end, save path if valid
      if (reel >= this.REELS) {
        if (currentPath.length >= this.MIN_WIN_LENGTH) {
          allPaths.push([...currentPath]);
        }
        return;
      }

      let foundMatch = false;

      // Check all positions in current reel
      for (let row = 0; row < this.ROWS; row++) {
        const symbol = symbolGrid[reel][row];

        // Check if symbols match (including wild substitution)
        if (this.symbolsMatch(targetSymbol, symbol)) {
          foundMatch = true;
          const newPath = [...currentPath, { reel, row }];
          findPaths(reel + 1, newPath);
        }
      }

      // If no match found in current reel, save current path if valid
      if (!foundMatch && currentPath.length >= this.MIN_WIN_LENGTH) {
        allPaths.push([...currentPath]);
      }
    };

    findPaths(startReel, [{ reel: startReel, row: startRow }]);
    return allPaths;
  }

  /**
   * Calculate scatter wins - Super Ace Style
   */
  private calculateScatterWins(symbolGrid: string[][], betAmount: number): WinLine[] {
    const scatterPositions: WinPosition[] = [];

    // Find all scatter positions
    for (let reel = 0; reel < this.REELS; reel++) {
      for (let row = 0; row < this.ROWS; row++) {
        if (symbolGrid[reel][row] === 'SCATTER') {
          scatterPositions.push({ reel, row });
        }
      }
    }

    // Need at least 3 scatters for a win
    if (scatterPositions.length >= 3) {
      const scatterMultiplier = this.getSymbolPayout('SCATTER', scatterPositions.length);
      const totalPayout = scatterMultiplier * betAmount; // Multiplies total bet
      
      return [{
        symbol: 'SCATTER',
        positions: scatterPositions,
        count: scatterPositions.length,
        ways: 1, // Scatters don't use ways
        payout: totalPayout,
        type: 'scatter'
      }];
    }

    return [];
  }

  /**
   * Check if two symbols match (including wild substitution)
   */
  private symbolsMatch(symbol1: string, symbol2: string): boolean {
    if (symbol1 === symbol2) return true;
    
    // Wilds substitute for all symbols except scatters
    if (symbol1 === 'WILD' && symbol2 !== 'SCATTER') return true;
    if (symbol2 === 'WILD' && symbol1 !== 'SCATTER') return true;
    
    return false;
  }

  /**
   * Get symbol payout from paytable
   */
  private getSymbolPayout(symbol: string, count: number): number {
    const symbolPayouts = PAYTABLE[symbol as keyof typeof PAYTABLE];
    if (!symbolPayouts) return 0;
    
    return symbolPayouts[count as keyof typeof symbolPayouts] || 0;
  }

  /**
   * Count total scatters in the grid
   */
  private countScatters(symbolGrid: string[][]): number {
    let count = 0;
    
    for (let reel = 0; reel < this.REELS; reel++) {
      for (let row = 0; row < this.ROWS; row++) {
        if (symbolGrid[reel][row] === 'SCATTER') {
          count++;
        }
      }
    }
    
    return count;
  }

  /**
   * Validate symbol grid structure
   */
  private validateGrid(symbolGrid: string[][]): boolean {
    if (!Array.isArray(symbolGrid)) return false;
    if (symbolGrid.length !== this.REELS) return false;

    for (let reel = 0; reel < this.REELS; reel++) {
      if (!Array.isArray(symbolGrid[reel])) return false;
      if (symbolGrid[reel].length !== this.ROWS) return false;
    }

    return true;
  }

  /**
   * Get paytable for display
   */
  public getPaytable(): typeof PAYTABLE {
    return PAYTABLE;
  }
}

export const superAceWinCalculator = new SuperAceWinCalculator();
